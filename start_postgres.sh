#!/bin/bash

# PostgreSQL 14 启动脚本
# 用于启动位于 /Volumes/lucky/data/postgresql@14 的PostgreSQL实例

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# PostgreSQL数据目录
PG_DATA_DIR="/Volumes/lucky/data/postgresql@14"

# 检查PostgreSQL是否已运行
check_postgres_running() {
    if pg_ctl -D "$PG_DATA_DIR" status > /dev/null 2>&1; then
        echo -e "${GREEN}PostgreSQL已经在运行${NC}"
        pg_ctl -D "$PG_DATA_DIR" status
        return 0
    else
        return 1
    fi
}

# 检查数据目录是否存在
check_data_directory() {
    if [ ! -d "$PG_DATA_DIR" ]; then
        echo -e "${RED}错误: PostgreSQL数据目录不存在: $PG_DATA_DIR${NC}"
        exit 1
    fi
    
    if [ ! -f "$PG_DATA_DIR/postgresql.conf" ]; then
        echo -e "${RED}错误: 数据目录中缺少postgresql.conf文件${NC}"
        exit 1
    fi
}

# 启动PostgreSQL
start_postgres() {
    echo -e "${YELLOW}正在启动PostgreSQL...${NC}"
    echo "数据目录: $PG_DATA_DIR"
    
    # 尝试启动
    if pg_ctl -D "$PG_DATA_DIR" start; then
        echo -e "${GREEN}PostgreSQL启动成功！${NC}"
        
        # 等待几秒钟确保完全启动
        sleep 3
        
        # 显示状态
        pg_ctl -D "$PG_DATA_DIR" status
        
        # 显示监听端口
        if command -v netstat > /dev/null 2>&1; then
            echo -e "${YELLOW}PostgreSQL监听端口:${NC}"
            netstat -an | grep :5432 || echo "未检测到5432端口监听"
        elif command -v lsof > /dev/null 2>&1; then
            echo -e "${YELLOW}PostgreSQL监听端口:${NC}"
            lsof -i :5432 || echo "未检测到5432端口监听"
        fi
    else
        echo -e "${RED}PostgreSQL启动失败！${NC}"
        echo -e "${YELLOW}请检查日志文件: $PG_DATA_DIR/log/postgresql-*.log${NC}"
        exit 1
    fi
}

# 主程序
main() {
    echo "PostgreSQL 14 启动脚本"
    echo "======================"
    
    # 检查pg_ctl命令
    if ! command -v pg_ctl > /dev/null 2>&1; then
        echo -e "${RED}错误: 未找到pg_ctl命令${NC}"
        echo "请确保PostgreSQL已正确安装"
        exit 1
    fi
    
    # 检查是否已运行
    if check_postgres_running; then
        exit 0
    fi
    
    # 检查数据目录
    check_data_directory
    
    # 启动PostgreSQL
    start_postgres
}

# 执行主程序
main "$@"