# 技术方案设计

## 架构概述

本模块基于Odoo 17，扩展销售订单（sale.order）及相关前台下单界面，实现订单商品的幅宽/数量校验与弹窗提醒。

## 技术选型
- 后端：Odoo 17，自定义Python模型与业务逻辑
- 前端：Odoo Web/Portal（QWeb/JS）弹窗交互

## 关键点
1. 产品Width（cm）字段的获取与判断
2. 订单行数量的判断
3. 前台与后台弹窗实现方式
4. Other Remarks字段的自动补充与权限控制

## 数据库/接口设计
- 复用product.product的Width字段（如无则需扩展）
- 复用sale.order.line的数量字段
- 复用sale.order的Other Remarks字段（如无则需扩展）

## 流程图

```mermaid
graph TD
    A[前台/后台下单] --> B{判断Width < 145?}
    B -- 是 --> C[弹窗提醒]
    B -- 否 --> D[正常流程]
    C --> E{前台Confirm?}
    E -- 是 --> F[Other Remarks加OK]
    E -- 否 --> D
    A --> G{判断数量<1码?}
    G -- 是且前台 --> H[弹窗提醒]
    G -- 否 --> D
```

## 安全性
- 仅后台可见“OK”标记，前台客户不可见

## 测试策略
- 单元测试：模型方法、弹窗逻辑
- 集成测试：下单全流程覆盖 