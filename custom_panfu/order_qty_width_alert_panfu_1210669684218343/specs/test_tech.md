# 技术测试说明

本说明用于指导开发者/测试人员如何对“订单数量/幅宽下单提醒”插件进行技术层面的测试，包括单元测试、集成测试、接口测试等。

## 1. 后端功能测试

### 1.1 后台订单行窄封弹窗提醒
- 步骤：
  1. 在Odoo后台新建销售订单，添加一条产品，选择幅宽（width_cm）小于145cm的产品。
  2. 观察是否弹出“窄封，请与客户确认数量OK”提示。
- 预期：弹窗正常出现。

### 1.2 SO Remarks字段写入
- 步骤：
  1. 在前台购物车界面，添加幅宽小于145cm的产品。
  2. 点击弹窗“确认”按钮。
  3. 在后台查看对应销售订单的SO Remarks（x_studio_so_remarks_1）字段。
- 预期：字段内容以“OK”开头。

## 2. 前端功能测试

### 2.1 前台购物车弹窗
- 步骤：
  1. 登录前台，添加幅宽小于145cm的产品到购物车。
  2. 修改数量为小于1。
- 预期：分别弹出窄封提醒和数量提醒弹窗。

## 3. 接口测试

- 使用Postman等工具，调用：
  - `/shop/cart/product_info`，传入product_id，检查返回width_cm。
  - `/shop/cart/set_ok_remark`，传入order_id，检查SO Remarks字段写入。

## 4. 自动化测试建议
- 可编写Odoo单元测试，模拟订单创建、购物车操作、接口调用，断言弹窗与字段写入逻辑。 