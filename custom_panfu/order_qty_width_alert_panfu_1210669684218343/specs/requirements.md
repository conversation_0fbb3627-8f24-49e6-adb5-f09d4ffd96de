# 需求文档

## 介绍

为提升下单准确性和客户服务体验，系统需在特定情形下（如窄封、小过1码）于前台和后台界面给予用户友好提示，确保订单信息完整、客户需求被充分确认。

## 需求

### 需求 1 - 窄封产品下单提示

**用户故事：** 作为销售或客户，当我在前台或后台下单时，如果所选产品幅宽小于145cm，系统应主动提醒我确认订购数量是否足够，避免因幅宽过窄导致数量不足。

#### 验收标准

1. While 在前台购物车界面添加商品时, when 产品Width（cm）字段值小于145, the 系统 shall 弹出对话框，提示“您订购的产品xxxxxx幅宽:yyy小于145cm，请确认订购的长度足够。”（xxxxxx为产品编号，yyy为Width值）。
2. While 在后台订单界面添加商品时, when 产品Width（cm）字段值小于145, the 系统 shall 弹出对话框，提示“窄封，请与客户确认数量OK”。

### 需求 2 - 小过1码下单提示

**用户故事：** 作为销售或客户，当我在前台下单时，如果某产品订购数量小于1码，系统应提醒我是否需要备注配色，避免遗漏关键信息。

#### 验收标准

1. While 在前台购物车界面添加商品时, when 产品订购数量小于1码, the 系统 shall 弹出对话框，提示“您订购的产品xxxxxx数量小于1Y, 如需配色，请记得填写Other Remarks”。
2. When 弹窗出现时, the 系统 shall 提供“确定”按钮或允许用户直接关闭弹窗。

### 需求 3 - 备注自动补充

**用户故事：** 作为销售或客户，在前台确认窄封提示后，系统应自动在Other Remarks字段最前面加上“OK”，且该标记仅后台可见，前台客户不可见，确保信息传递但不影响客户体验。

#### 验收标准

1. When 用户在前台点击“Confirm”按钮, the 系统 shall 在Other Remarks字段最前面加“OK”，且不覆盖原有内容。
2. When 订单信息被后台查看时, the 系统 shall 显示“OK”标记；当前台客户查看时, the 系统 shall 不显示“OK”标记。 