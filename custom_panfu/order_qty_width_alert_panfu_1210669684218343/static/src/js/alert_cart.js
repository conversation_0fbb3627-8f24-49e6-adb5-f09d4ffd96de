odoo.define('order_qty_width_alert_panfu_1210669684218343.alert_cart', function (require) {
    'use strict';
    var publicWidget = require('web.public.widget');
    var ajax = require('web.ajax');

    publicWidget.registry.CartAlert = publicWidget.Widget.extend({
        selector: '.oe_website_sale',
        events: {
            'change input.js_quantity,input[name="add_qty"]': '_onQtyChange',
            'click .a-submit': '_onAddToCart',
        },
        _onQtyChange: function (ev) {
            var $input = $(ev.currentTarget);
            var $line = $input.closest('tr[data-line-id]');
            var productId = $line.data('product-id');
            var qty = parseFloat($input.val());
            if (!productId || isNaN(qty)) return;
            ajax.jsonRpc('/shop/cart/product_info', 'call', {product_id: productId}).then(function (data) {
                if (data.width_cm && data.width_cm < 145) {
                    CartAlert._showWidthAlert(productId);
                }
                if (qty < 1) {
                    CartAlert._showQtyAlert(productId);
                }
            });
        },
        _onAddToCart: function (ev) {
            // 可选：拦截添加到购物车按钮
        },
    });

    var CartAlert = {
        _showWidthAlert: function(productId) {
            if ($('#cart-width-alert').length) return;
            var $dialog = $(
                '<div id="cart-width-alert" class="modal" tabindex="-1" role="dialog">' +
                '<div class="modal-dialog" role="document"><div class="modal-content">' +
                '<div class="modal-header"><h5 class="modal-title">窄封提醒</h5></div>' +
                '<div class="modal-body">您订购的产品幅宽小于145cm，请确认订购的长度足够。</div>' +
                '<div class="modal-footer">' +
                '<button type="button" class="btn btn-primary" id="cart-width-confirm">确认</button>' +
                '<button type="button" class="btn btn-secondary" data-dismiss="modal">更改数量</button>' +
                '</div></div></div></div>'
            );
            $dialog.appendTo('body').modal('show');
            $dialog.find('#cart-width-confirm').on('click', function() {
                var orderId = $('form.oe_cart input[name="sale_order_id"]').val() || $('.oe_website_sale').data('order-id');
                if (orderId) {
                    ajax.jsonRpc('/shop/cart/set_ok_remark', 'call', {order_id: orderId});
                }
                $dialog.modal('hide');
            });
            $dialog.on('hidden.bs.modal', function() { $dialog.remove(); });
        },
        _showQtyAlert: function(productId) {
            if ($('#cart-qty-alert').length) return;
            var $dialog = $(
                '<div id="cart-qty-alert" class="modal" tabindex="-1" role="dialog">' +
                '<div class="modal-dialog" role="document"><div class="modal-content">' +
                '<div class="modal-header"><h5 class="modal-title">数量提醒</h5></div>' +
                '<div class="modal-body">您订购的产品数量小于1Y, 如需配色，请记得填写Other Remarks。</div>' +
                '<div class="modal-footer">' +
                '<button type="button" class="btn btn-primary" data-dismiss="modal">确定</button>' +
                '</div></div></div></div>'
            );
            $dialog.appendTo('body').modal('show');
            $dialog.on('hidden.bs.modal', function() { $dialog.remove(); });
        },
    };

    return CartAlert;
}); 