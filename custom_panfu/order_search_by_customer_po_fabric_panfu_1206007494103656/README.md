# Order Search by Customer PO and Fabric

This module extends the Odoo e-commerce portal to allow customers to search their orders using either Customer PO or Fabric Number.

## Features

1. Adds Customer PO and Fabric Number fields to sale orders
2. Extends the portal search functionality to include these fields
3. Displays Customer PO and Fabric Number in the portal order lists
4. Allows customers to search orders by Customer PO or Fabric Number

## Installation

1. Copy this module to your Odoo addons directory
2. Update the apps list in Odoo
3. Install the "Order Search by Customer PO and Fabric" module

## Configuration

No additional configuration is required. The module automatically extends the existing portal functionality.

## Usage

1. Customers can enter a Customer PO or Fabric Number in the search box on the "My Orders" or "Quotations" pages
2. The system will filter orders that match the entered value in either field
3. Customer PO can be entered during checkout or updated later in the order

## Technical Notes

- Extends the sale.order model with customer_po and fabric_number fields
- Overrides portal controllers to include these fields in search
- Modifies portal templates to display the new fields