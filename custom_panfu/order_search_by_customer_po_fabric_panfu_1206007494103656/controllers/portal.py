from odoo import http, _
from odoo.http import request
from odoo.addons.portal.controllers.portal import pager as portal_pager
from odoo.addons.sale.controllers.portal import CustomerPortal


class CustomerPortalExtended(CustomerPortal):

    def _prepare_orders_domain(self, partner):
        domain = super()._prepare_orders_domain(partner)
        customer_po = request.params.get('customer_po')
        fabric_number = request.params.get('fabric_number')

        if customer_po:
            line_orders = request.env['sale.order.line'].search([('customer_po', 'ilike', customer_po)]).mapped('order_id').ids
            if line_orders:
                domain += [('id', 'in', line_orders)]
            else:
                domain += [('id', '=', 0)]
        if fabric_number:
            line_orders = request.env['sale.order.line'].search([('fabric_number', 'ilike', fabric_number)]).mapped('order_id').ids
            if line_orders:
                domain += [('id', 'in', line_orders)]
            else:
                domain += [('id', '=', 0)]
        return domain

    def _prepare_quotations_domain(self, partner):
        domain = super()._prepare_quotations_domain(partner)
        customer_po = request.params.get('customer_po')
        fabric_number = request.params.get('fabric_number')

        if customer_po:
            line_orders = request.env['sale.order.line'].search([('customer_po', 'ilike', customer_po)]).mapped('order_id').ids
            if line_orders:
                domain += [('id', 'in', line_orders)]
            else:
                domain += [('id', '=', 0)]
        if fabric_number:
            line_orders = request.env['sale.order.line'].search([('fabric_number', 'ilike', fabric_number)]).mapped('order_id').ids
            if line_orders:
                domain += [('id', 'in', line_orders)]
            else:
                domain += [('id', '=', 0)]
        return domain

    def _get_sale_searchbar_sortings(self):
        """Add custom fields to searchbar sorting options"""
        sortings = super()._get_sale_searchbar_sortings()
        sortings.update({
            'customer_po': {'label': _('Customer PO'), 'order': 'customer_po'},
            'fabric_number': {'label': _('Fabric Number'), 'order': 'fabric_number'},
        })
        return sortings

    def _prepare_sale_portal_rendering_values(
        self, page=1, date_begin=None, date_end=None, sortby=None, 
        quotation_page=False, **kwargs
    ):
        values = super()._prepare_sale_portal_rendering_values(
            page, date_begin, date_end, sortby, quotation_page, **kwargs
        )
        values['search'] = request.params.get('search', '')
        values['customer_po'] = request.params.get('customer_po', '')
        values['fabric_number'] = request.params.get('fabric_number', '')
        # 确保 portal 搜索框显示
        values['searchbar_inputs'] = {
            'all': {'input': 'all', 'label': _('全部')}
        }
        # 聚合明细行 customer_po
        orders = values.get('orders') or values.get('quotations')
        customer_po_agg_dict = {}
        if orders:
            for order in orders:
                po_list = order.order_line.mapped('customer_po')
                customer_po_agg_dict[order.id] = ', '.join([po for po in po_list if po])
        values['customer_po_agg_dict'] = customer_po_agg_dict
        return values