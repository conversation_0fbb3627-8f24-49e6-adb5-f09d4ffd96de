<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Extend the portal searchbar to add customer PO and fabric number search -->
    <template id="portal_searchbar_extended" inherit_id="portal.portal_searchbar" name="Extended Portal Search Bar">
        <xpath expr="//form/div/input[@name='search']" position="attributes">
            <attribute name="placeholder">Search by Order, Customer PO or Fabric #</attribute>
        </xpath>
    </template>

    <!-- Extend the quotations template to add customer PO and fabric number columns -->
    <template id="portal_my_quotations_extended" inherit_id="sale.portal_my_quotations" name="Extended Portal Quotations">
        <xpath expr="//thead/tr" position="inside">
            <th>Customer PO</th>
            <th>Fabric #</th>
        </xpath>
        <xpath expr="//t[@t-foreach='quotations']/tr" position="inside">
            <td><span t-field="quotation.customer_po"/></td>
            <td><span t-field="quotation.fabric_number"/></td>
        </xpath>
    </template>

    <!-- Extend the orders template to add customer PO and fabric number columns -->
    <template id="portal_my_orders_extended" inherit_id="sale.portal_my_orders" name="Extended Portal Orders">
        <xpath expr="//thead/tr" position="inside">
            <th>Customer PO</th>
            <th>Fabric #</th>
        </xpath>
        <xpath expr="//t[@t-foreach='orders']/tr" position="inside">
            <td><t t-esc="customer_po_agg_dict.get(order.id, '')"/></td>
            <td><span t-field="order.fabric_number"/></td>
        </xpath>
    </template>

    <!-- 只保留扩展 portal 搜索条的模板 -->
    <template id="portal_searchbar_extend_customer_po_fabric" inherit_id="portal.portal_searchbar" name="Extend Portal Searchbar with Customer PO and Fabric">
        <!-- 移除原有 searchbar_inputs 相关内容（button、dropdown、input、search） -->
        <xpath expr="//form[contains(@class, 'o_portal_search_panel')]/div[contains(@class, 'input-group')]/button[@type='button']" position="replace"/>
        <xpath expr="//form[contains(@class, 'o_portal_search_panel')]/div[contains(@class, 'input-group')]/div[contains(@class, 'dropdown-menu')]" position="replace"/>
        <xpath expr="//form[contains(@class, 'o_portal_search_panel')]/div[contains(@class, 'input-group')]/input[@name='search']" position="replace"/>
        <xpath expr="//form[contains(@class, 'o_portal_search_panel')]/div[contains(@class, 'input-group')]/button[@type='submit']" position="replace"/>
        <!-- 只保留自定义输入框和搜索按钮 -->
        <xpath expr="//form[contains(@class, 'o_portal_search_panel')]/div[contains(@class, 'input-group')]" position="inside">
            <input type="text" name="customer_po" class="form-control" placeholder="客户采购单号" t-att-value="customer_po or ''"/>
            <input type="text" name="fabric_number" class="form-control" placeholder="面料编号" t-att-value="fabric_number or ''"/>
            <button class="btn btn-secondary o_wait_lazy_js" type="submit">
                <span class="oi oi-search"/>
            </button>
        </xpath>
        <!-- 搜索表单整体居中 -->
        <xpath expr="//form[contains(@class, 'o_portal_search_panel')]" position="attributes">
            <attribute name="class">o_portal_search_panel col-md-7 col-xl-6 mx-auto justify-content-center d-flex</attribute>
        </xpath>
        <xpath expr="//form[contains(@class, 'o_portal_search_panel')]/div[contains(@class, 'input-group')]" position="attributes">
            <attribute name="class">input-group w-100 justify-content-center</attribute>
        </xpath>
    </template>

    <!-- 保留原有扩展订单列表和表头的模板 -->
</odoo>