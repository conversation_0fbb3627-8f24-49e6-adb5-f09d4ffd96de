# 实施计划

- [ ] 1. 初始化插件目录与基础文件
  - 创建 __init__.py、__manifest__.py、models 目录等基础结构
  - _需求: 1

- [ ] 2. 扩展 stock.inventory.line/stock.move.line 模型，增加批号必填后端校验
  - 在 create、write、action_validate 等方法中实现批号校验逻辑
  - _需求: 1

- [ ] 3. 扩展批量导入和API接口的批号校验
  - 保证通过导入或API操作时同样强制批号必填
  - _需求: 1

- [ ] 4. （可选）前端视图XML增强，提升用户体验
  - 对相关表单字段设置 required 属性（如有必要）
  - _需求: 1

- [ ] 5. 错误提示优化
  - 明确指出未填写批号的产品或行，便于用户定位
  - _需求: 1

- [ ] 6. 单元测试与集成测试
  - 覆盖盘点单行的新增、编辑、保存、验证、批量导入、API等场景
  - _需求: 1

- [ ] 7. 文档与用户指引
  - 更新操作说明，明确批号必填规则
  - _需求: 1 