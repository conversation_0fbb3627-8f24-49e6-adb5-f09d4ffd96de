# 需求文档

## 介绍

为提升数据完整性和合规性，确保所有按批次追踪（Lot/Serial Number）的产品在Odoo库存盘点（Physical Inventory）或库存调整（Inventory Adjustment）时，必须填写批号（Lot/Serial Number），否则不能保存或提交盘点单。当前系统仅弹出警告，未强制要求，存在数据缺失风险。

## 需求

### 需求 1 - 盘点时批号必填

**用户故事：**  
作为仓库管理员，当我对启用批次管理的产品进行库存盘点或库存调整时，我希望系统强制要求填写批号，否则不能保存或提交盘点单，以保证数据的完整性和后续追溯的合规性。

#### 验收标准

1. While 用户在Odoo进行库存盘点或库存调整时，when 选择的产品启用了批次追踪，the 系统 shall 强制要求填写批号（Lot/Serial Number），否则不能保存或提交盘点单，并给出明确的错误提示。
2. While 用户通过批量导入或API进行盘点数据录入时，when 选择的产品启用了批次追踪且未填写批号，the 系统 shall 阻止数据导入，并返回错误信息。
3. While 用户对未启用批次追踪的产品进行盘点时，the 系统 shall 不强制要求填写批号，允许正常保存和提交。
4. While 用户在盘点单中新增、编辑、保存、提交盘点行时，the 系统 shall 在所有相关操作点进行批号必填校验，确保无遗漏。
5. While 校验未通过时，the 系统 shall 给出清晰的错误提示，指明哪些产品/行未填写批号。 