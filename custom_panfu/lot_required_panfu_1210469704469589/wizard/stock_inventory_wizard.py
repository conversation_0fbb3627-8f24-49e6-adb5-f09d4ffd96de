from odoo import models, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class StockInventoryAdjustmentName(models.TransientModel):
    _inherit = 'stock.inventory.adjustment.name'

    def action_apply(self):
        """应用调整前验证批次"""
        _logger.info("=== Validating lot requirements before applying inventory adjustment ===")
        
        # 获取正在调整的 quants
        quant_ids = self.env.context.get('active_ids', [])
        if not quant_ids:
            # 尝试从其他context key获取
            quant_ids = self.env.context.get('quant_ids', [])
        
        quants = self.env['stock.quant'].browse(quant_ids)
        
        # 验证每个quant
        errors = []
        for quant in quants:
            # 只检查正在调整库存的记录
            if hasattr(quant, 'inventory_quantity_set') and quant.inventory_quantity_set:
                if quant.product_id.tracking in ('lot', 'serial') and not quant.lot_id:
                    errors.append(f'- {quant.product_id.display_name}')
        
        if errors:
            raise UserError(_(
                '以下产品启用了批次/序列号管理，必须填写批次号：\n%s'
            ) % '\n'.join(errors))
        
        return super().action_apply()
