# -*- coding: utf-8 -*-
from odoo.tests.common import TransactionCase
from odoo.tests import tagged

@tagged('post_install', '-at_install')
class TestStockMoveLineViews(TransactionCase):

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        
        # 获取视图引用
        cls.transfer_view = cls.env.ref(
            'artextile_stock_enhancement.view_stock_move_line_operation_tree_transfer_inherit'
        )
        cls.receipt_view = cls.env.ref(
            'artextile_stock_enhancement.view_stock_move_line_detailed_operation_tree_receipt_inherit'
        )

    def test_transfer_view_inheritance(self):
        """测试转库视图的继承是否正确"""
        self.assertTrue(self.transfer_view.active)
        self.assertEqual(self.transfer_view.model, 'stock.move.line')
        self.assertEqual(
            self.transfer_view.inherit_id.xml_id,
            'stock.view_stock_move_line_operation_tree'
        )

    def test_receipt_view_inheritance(self):
        """测试收货视图的继承是否正确"""
        self.assertTrue(self.receipt_view.active)
        self.assertEqual(self.receipt_view.model, 'stock.move.line')
        self.assertEqual(
            self.receipt_view.inherit_id.xml_id,
            'stock.view_stock_move_line_detailed_operation_tree'
        )

    def test_field_order_in_transfer(self):
        """测试转库界面字段顺序"""
        arch = self.transfer_view.arch_db
        
        # 检查关键字段存在
        self.assertIn('product_id', arch)
        self.assertIn('x_studio_vendor_item_no', arch)
        self.assertIn('location_dest_id', arch)
        self.assertIn('location_id', arch)
        self.assertIn('quantity', arch)
        self.assertIn('product_uom_id', arch)

    def test_field_order_in_receipt(self):
        """测试收货界面字段顺序"""
        arch = self.receipt_view.arch_db
        
        # 检查新增字段存在
        self.assertIn('shipping_sch_id', arch)
        self.assertIn('gross_unit', arch)
        self.assertIn('selvedge_id', arch)
        self.assertIn('lot_name', arch)

    def test_optional_fields_hidden(self):
        """测试可选字段的默认隐藏状态"""
        arch = self.receipt_view.arch_db
        
        # 检查应隐藏的字段
        hidden_fields = [
            'available_quantity',
            'display_warning',
            'secondary_uom_qty',
            'secondary_uom_id',
            'x_studio_po_description'
        ]
        
        for field in hidden_fields:
            self.assertIn(f'{field}" optional="hide"', arch)

    def test_shipping_schedule_sortable(self):
        """测试Shipping Schedule支持排序"""
        arch = self.receipt_view.arch_db
        self.assertIn('shipping_sch_id', arch)
        # 检查是否有排序相关属性
        self.assertIn('many2one', arch)  # 确保字段类型正确