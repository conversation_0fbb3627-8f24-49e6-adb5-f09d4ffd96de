{
    "name": "Artextile Stock Move Line UI Enhancement",
    "version": "********.0",
    "category": "Inventory/Inventory",
    "summary": "Enhance stock move line detailed operation UI with custom field order and display settings",
    "description": """
        This module enhances the stock move line detailed operation interface with:
        
        1. Transfer Interface:
           - Reordered fields: Product, Vendor Item No, To, Pick From, Demand, Quantity, Unit of Measure
           - Hidden by default: Available Quantity, Display Warning, ETA, Exmill date, Updated Exmill, Secondary qty, Secondary uom, PO Description
        
        2. Receipt Interface:
           - Added fields: Shipping Schedule, Gross, Selvedge, Lot/Serial Number Name
           - Reordered fields with new additions
           - Proper sorting support for Shipping Schedule
        
        3. Clean implementation avoiding Studio interference
    """,
    "author": "Artextile Development Team",
    "website": "https://www.artextilestore.com",
    "category": "Inventory/Inventory",
    "depends": [
        "stock",
        "purchase_misc_jsi",  # For shipping_sch_id
        "mrp",                # For description_bom_line
        "studio_customization"  # To handle Studio conflicts
    ],
    "data": [
        "views/stock_move_line_transfer_inherit.xml",
        "views/stock_move_line_receipt_inherit.xml",
    ],
    "installable": True,
    "application": False,
    "auto_install": False,
    "license": "LGPL-3",
}