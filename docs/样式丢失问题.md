## Odoo 样式问题总结

### 问题出现的原因

1. **数据库记录与实际文件不匹配**
   - 数据库中的 `ir_attachment` 表记录了文件路径，但实际文件在 filestore 目录中不存在
   - 这通常发生在数据迁移、备份恢复或系统升级过程中

2. **文件路径记录错误**
   - 数据库中的 `store_fname` 字段包含了错误的文件路径
   - 例如：`ea/ea594e8fdfd5976cdb8a323401f6011b59b03a97ea594e8fdfd5976cdb8a323401f6011b59b03a97`（重复的文件名）

3. **Assets 文件缺失**
   - Odoo 的 assets 文件（CSS/JS）在 filestore 中丢失
   - 但数据库记录仍然指向这些不存在的文件

### 解决步骤

#### 第一步：诊断问题
```bash
# 检查错误日志
curl -I http://localhost:8069/web/assets/1/d3d2a79/web.assets_frontend.min.css
# 返回 500 错误

# 查找数据库记录
psql -d odoo_restored -c "SELECT id, name, store_fname FROM ir_attachment WHERE name = 'web.assets_frontend.min.css';"
```

#### 第二步：修复文件路径
```bash
# 修复重复的文件路径记录
psql -d odoo_restored -c "UPDATE ir_attachment SET store_fname = SUBSTRING(store_fname FROM 1 FOR POSITION('/' IN store_fname) + 40) WHERE store_fname LIKE '%/%' AND LENGTH(store_fname) > 80;"
```

#### 第三步：删除无效记录
```bash
# 删除不存在的文件记录
psql -d odoo_restored -c "DELETE FROM ir_attachment WHERE id = 378902;"
psql -d odoo_restored -c "DELETE FROM ir_attachment WHERE id IN (378904, 378890, 378911);"
```

#### 第四步：重启服务
```bash
# 重启 Odoo 服务，触发 assets 重新生成
pkill -f "python3.*odoo-bin"
python3 ./odoo/odoo-bin -c odoo.conf -d odoo_restored
```

### 根本原因分析

1. **数据迁移问题**：从备份恢复数据时，文件路径记录可能损坏
2. **文件系统问题**：filestore 目录中的文件丢失，但数据库记录未清理
3. **Assets 生成问题**：Odoo 的 assets 系统在生成文件时出现错误

### 预防措施

1. **定期备份**：确保 filestore 和数据库同步备份
2. **验证完整性**：恢复数据后验证文件路径的正确性
3. **监控日志**：关注 Odoo 日志中的文件访问错误
4. **清理机制**：定期清理无效的数据库记录

### 验证解决

```bash
# 测试静态文件访问
curl -I http://localhost:8069/web/assets/1/d3d2a79/web.assets_frontend.min.css
# 应该返回 200 OK

curl -I http://localhost:8069/web/assets/7a7e193/web.assets_web.min.css
# 应该返回 200 OK
```

这个问题的核心是**数据库记录与实际文件系统不同步**，通过清理无效记录并重启服务，让 Odoo 重新生成正确的 assets 文件来解决。