{
    'name': 'Product PO Status Enhanced - panfu',
    'version': '********.0',  # Version bump for optimizations
    'author': 'panfu',
    'category': 'Inventory/Inventory',
    'summary': 'Enhanced PO QTY button with multi-status support and optimizations',
    'description': '''
        Enhanced Product PO Status Display
        ==================================

        This module extends the product detail page PO QTY button (action_view_pending_po_moves)
        to support displaying purchase order lines with multiple statuses:

        * **Waiting**: Waiting for availability (addresses the main business requirement)
        * **Confirmed**: Confirmed and ready to process
        * **Partially Available**: Some quantity is available
        * **Assigned**: Fully available for processing

        Key Features:
        * Enhanced view with better field organization and visual indicators
        * Improved error handling and validation
        * Performance optimizations for large datasets
        * Better user experience with contextual information

        Business Context:
        This addresses Asana task 1210669684218343 where "Waiting Availability" status
        moves were not being displayed in the PO QTY button, causing incomplete
        visibility of purchase order status.

        Technical Improvements:
        * Code cleanup and documentation
        * Constants for status management
        * Enhanced error handling
        * Optimized domain queries
        * Better view organization with optional fields
    ''',
    'depends': [
        'product_misc_jsi',
        'stock',
    ],
    'sequence': 1000,  # Ensure this loads after product_misc_jsi
    'data': [
        'views/stock_move_views.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
    'license': 'LGPL-3',
}