from odoo import _, models
from odoo.exceptions import UserError
from odoo.tools.misc import format_date


class AccountMove(models.Model):
    _inherit = "account.move"

    # ref: addons/account/models/account_move.py (line 1787) -> AccountMove._check_fiscalyear_lock_date(self)
    def _check_fiscalyear_lock_date(self):
        super()._check_fiscalyear_lock_date()
        for move in self:
            lock_start_date = move.company_id._get_user_fiscal_lock_start_date()
            lock_end_date = move.company_id._get_user_fiscal_lock_end_date()
            if move.date < lock_start_date or move.date > lock_end_date:
                message = _(
                    "Accounting period not open. You can only add, modify and post entries from %s to %s."
                    "Contact the Admin for more information.",
                    format_date(self.env, lock_start_date),
                    format_date(self.env, lock_end_date),
                )
                raise UserError(message)
        return True
