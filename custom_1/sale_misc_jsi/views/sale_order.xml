<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <record id="view_order_form_sale_misc_jsi" model="ir.ui.view">
            <field name="name">sale.order.form.sale.misc.jsi</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form" />
            <field name="arch" type="xml">
                <field name="partner_id" position="after">
                    <field name="full_name" />
                </field>
                <field name="partner_shipping_id" position="after">
                    <label for="sale_uom_id" invisible="state in ['sale', 'done','cancel']" />
                    <div invisible="state in ['sale', 'done','cancel']" class="o_row">
                        <field name="sale_uom_id" class="oe_inline" />
                        <button
                            name="button_mass_update_uom"
                            string="Update UOM"
                            type="object"
                            class="btn-link mb-1 px-0"
                            icon="fa-refresh"
                            help="This will change the UOM of order line which has same categoy as given UOM"
                        />
                    </div>
                </field>
            </field>
        </record>
    </data>
</odoo>
