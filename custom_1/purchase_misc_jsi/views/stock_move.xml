<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <record id="view_picking_form_purchase_misc_jsi" model="ir.ui.view">
        <field name="name">stock.picking.form.purchase.misc.jsi</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form" />
        <field name="arch" type="xml">
            <xpath expr="//field[@name='move_ids_without_package']/tree/field[@name='product_uom']" position="after">
                <field name="ready_date" optional="hide" />
                <field name="shipping_sch_id" optional="hide" options="{'no_open': True}" />
            </xpath>
        </field>
    </record>
    <record id="view_move_line_tree_purchase_misc_jsi" model="ir.ui.view">
        <field name="name">view.move.line.tree.purchase.misc.jsi</field>
        <field name="model">stock.move.line</field>
        <field name="inherit_id" ref="stock.view_move_line_tree" />
        <field name="arch" type="xml">
            <field name="state" position="after">
                <field name="shipping_sch_id" optional="hide" />
            </field>
        </field>
    </record>
</odoo>
