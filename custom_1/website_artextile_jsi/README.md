# Ps-Tech module : Website Customisation Artextile

## ********.0

Product display name change on website
Hide sensitive details from non-user on website
Website multi UOM
New cart line for every product when click on ADD TO CART
Advanced Quantity Management
    - Limit Maximum selling Quantity
    - Allowed to sell product with decimal place
    - Display Secondary price on product list
    - 0.125 and 0.05 quantity check
    - Block Add to cart button if ordered Quantity is <= 0
    - Display list_price with applied pricelist for secondary uom
    - Change availability message

## ********.1
-   Reformat stock details in list and form view of product in website

## ********.2
-   Add new fields on customer address form on website

## ********.3
-   Add new field on user address form on portal view
-   Add new field on `Extra step`

## ********.4
-   Make name field readonly on portal view

## ********.5
-   Set Default UOM on product as per the last sale order line of last sale order

## ********.6
-   Translatable the stock availibility status message

## ********.7
-   Simplify search product feature on website
-   Remove category filter during product search in website

## ********.0
-   Different Payment methods for customer

## ********.1
-   Allow user to enable/disable Balance section on Payment Screen

## ********.2
-   Render only allowed Payment Method on
    - Ecommerce Payment page(Improved existing code)
    - Invoice Portal Payment page
    - Direct payment page by link

## ********.0
-   New mechanism for Sold Out status
    - Added new field on configuration for setting Stock Quantity Threshold
    - It will prevent selling product if product's onhand quantity goes below the Stock Quantity Threshold
    - It will disable the button and change status to Sold Out
-   Fix the issue on cart line related to condition for onhand quantity checking
## ********.1
-   Make name field readonly on Shop Address page
-   Pass default value for `name` field as it's required field

## ********.2
-   Remove Billing Address from the Address section on checkout page
-   Remove Billing Address from the Payment section
-   Remove Edit button from Address box from Portal page
-   Show Edit button on Shipping address based on condition
-   Change the Address format

## ********.3
-   Display price in Yard with Pricelist on Website product listing page (/shop)
-   Added condition for display Price with Discount and Without discount(Pricelist with -/+ discount) on Website product listing page and Cart line

## ********.4
-   No need to disable `Add to cart` if `Availability` is `Sell regardless of inventory`

## ********.5
-   Disply USD price on product detail page

## ********.6
-   Display `shipping_info` on website product listing and product detail page

## ********.7
-   Added Customer Performace chart on Portal view

## ********.8
-   Display `product_feature` on website product detail page

## ********.0
-   Migrate to 17.0

## ********.1
[Maintenance]
- Portal payment page
- Availability status for Public user

## ********.2
[Maintenance: 4857427]
- Auto select UOM based on last ordered on website
