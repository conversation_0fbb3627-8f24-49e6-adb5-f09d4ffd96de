import json

from lxml import etree

from odoo import api, models


class SalePurchaseMixin(models.AbstractModel):
    """Mixin class for Sale and Purchase to add modifier to make form readonly when state is done"""

    _name = "sale.purchase.mixin"
    _description = "Sale Purchase Mixin"

    @api.model
    def _get_view(self, view_id=None, view_type='form', **options):
        arch, view = super()._get_view(view_id, view_type, **options)

        # For compute field to check if it's already `readonly` or not
        # Ref: https://github.com/odoo/enterprise/blob/17.0/worksheet/models/worksheet_template.py#L406
        form_view_fields = {fname: field_info for fname, field_info in self.fields_get().items()}

        if view_type == "form":
            order_line_fields = self.order_line._fields.keys()
            for node in arch.xpath('//field[not(ancestor::field)]'):
                """
                    By default it will load all the fields of form view.
                    ISSUE: This will applied on all fields including sale.order.line and purchase.order.line
                    For purchase.order: we have same `state` field on purchase.order.line, so no issue but no needed as there is already readonly applied
                    For sale.order: we don't have `locked` on sale.order.line, so we need to bypass it's fields
                    We also need to check if its already `readonly` in field level by `fields_get`, if yes, nothing to do. (Most probably compute field)
                """
                if node.get('name') in order_line_fields:
                    continue
                if self._name == 'purchase.order':
                    if not node.attrib.get('readonly'):
                        if form_view_fields.get(node.attrib.get('name'), {}).get('readonly'):
                            continue
                        node.attrib["readonly"] = "state == 'done'"
                    elif "state == 'done'" not in node.attrib["readonly"]:
                        node.attrib["readonly"] += " or state == 'done'"
                else:
                    if not node.attrib.get('readonly'):
                        if form_view_fields.get(node.attrib.get('name'), {}).get('readonly'):
                            continue
                        node.attrib["readonly"] = "locked"
                    elif "locked" not in node.attrib["readonly"]:
                        node.attrib["readonly"] += " or locked"
        return arch, view


class SaleOrder(models.Model):
    _name = "sale.order"
    _inherit = ["sale.order", "sale.purchase.mixin"]


class PurchaseOrder(models.Model):
    _name = "purchase.order"
    _inherit = ["purchase.order", "sale.purchase.mixin"]
