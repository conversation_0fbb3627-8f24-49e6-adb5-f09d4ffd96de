<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <record id="purchase_order_form_partner_misc_jsi" model="ir.ui.view">
            <field name="name">purchase.order.form.partner.misc.jsi</field>
            <field name="model">purchase.order</field>
            <field name="inherit_id" ref="purchase.purchase_order_form" />
            <field name="arch" type="xml">
                <field name="partner_id" position="attributes">
                    <attribute name="domain">['|', ('company_id', '=', False), ('company_id', '=', company_id), ('parent_id', '=', False), ('supplier_rank','&gt;', 0)]</attribute>
                </field>
            </field>
        </record>
    </data>
</odoo>
