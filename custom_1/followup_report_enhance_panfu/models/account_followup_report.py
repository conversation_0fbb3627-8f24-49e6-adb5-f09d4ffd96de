from odoo import models
import datetime

class AccountFollowupReport(models.AbstractModel):
    _inherit = 'account.followup.report'

    def _get_followup_report_lines(self, options):
        """
        Override the followup report lines generation to fix Date/Due Date display
        for non-invoice documents and improve sorting
        """
        # 获取原始lines
        lines = super()._get_followup_report_lines(options)
        
        # 增强：修正Date/Due Date/排序
        for line in lines:
            if line.get('type') == 'unreconciled_aml' and line.get('account_move'):
                aml = self.env['account.move.line'].browse(line['id'])
                move = aml.move_id
                
                # 非发票单据，Date和Due Date都用move.date
                if move.move_type not in ('out_invoice', 'in_invoice', 'out_refund', 'in_refund'):
                    for idx, col in enumerate(line['columns']):
                        if idx in (0, 1):  # Date和Due Date列
                            date = move.date and move.date.strftime('%m/%d/%Y') or ''
                            col['name'] = date
        
        # 按Date（第1列）排序
        def parse_date(line):
            try:
                if line.get('columns') and len(line['columns']) > 0:
                    date_str = line['columns'][0]['name']
                    if date_str:
                        # 尝试解析不同的日期格式
                        for fmt in ('%m/%d/%Y', '%d/%m/%Y', '%Y-%m-%d'):
                            try:
                                return datetime.datetime.strptime(date_str, fmt)
                            except ValueError:
                                continue
                return datetime.datetime.max
            except Exception:
                return datetime.datetime.max
        
        # 只对 unreconciled_aml 类型的行进行排序
        unreconciled_lines = [line for line in lines if line.get('type') == 'unreconciled_aml']
        other_lines = [line for line in lines if line.get('type') != 'unreconciled_aml']
        
        # 排序 unreconciled_aml 行
        unreconciled_lines = sorted(unreconciled_lines, key=parse_date)
        
        # 重新组合，保持其他行的原始位置
        result_lines = []
        unreconciled_index = 0
        
        for line in lines:
            if line.get('type') == 'unreconciled_aml':
                if unreconciled_index < len(unreconciled_lines):
                    result_lines.append(unreconciled_lines[unreconciled_index])
                    unreconciled_index += 1
            else:
                result_lines.append(line)
        
        return result_lines
