from odoo import models, fields

class AccountMoveLine(models.Model):
    _inherit = "account.move.line"

    communication_ar = fields.Char(string="Communication", compute="_compute_communication_ar")

    def _compute_communication_ar(self):
        """
        Communication列增强：客户名、单号、备注等，兼容v14风格
        """
        for line in self:
            move = line.move_id
            partner_name = move.partner_id and move.partner_id.name or ''
            move_name = move.name or ''
            invoice_origin = move.invoice_origin or ''
            ref = move.ref or ''
            # 备注等可按实际业务补充
            communication = f"{move_name}-{partner_name}"
            if invoice_origin:
                communication += f", {invoice_origin}"
            if ref:
                communication += f", {ref}"
            line.communication_ar = communication 