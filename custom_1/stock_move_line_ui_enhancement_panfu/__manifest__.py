{
    'name': 'Stock Move Line UI Enhancement - panfu',
    'version': '********.0',
    "author": "panfu",
    "category": "Inventory/Inventory",
    "summary": "Enhance stock move line detailed operation UI with custom field order and display settings",
    "description": """
        This module enhances the stock move line detailed operation interface with:
        
        1. Transfer Interface:
           - Reordered fields: Product, Vendor Item No, To, Pick From, Demand, Quantity, Unit of Measure
           - Hidden by default: Available Quantity, Display Warning, ETA, Exmill date, Updated Exmill, Secondary qty, Secondary uom, PO Description
        
        2. Receipt Interface:
           - Added fields: Shipping Schedule, Gross, Selvedge, Lot/Serial Number Name
           - Reordered fields with new additions
           - Proper sorting support for Shipping Schedule
        
        3. Clean implementation avoiding Studio interference
        
        Author: panfu
        Asana Task: 1208337958369478
    """,
    'depends': [
        "stock",
        "stock_barcode",      # For quant_id field functionality
        "purchase_misc_jsi",  # For shipping_sch_id - 不在当前代码中使用
        "mrp",                # For description_bom_line - 不在当前代码中使用
        "secondary_uom_jsi",  # For secondary_uom_qty and secondary_uom_id - 不在当前代码中使用
        "lot_cust_jsi",       # For gross_unit and selvedge_id - 不在当前代码中使用
    ],
    'data': [
        'views/stock_move_line_views.xml',
    ],
}
