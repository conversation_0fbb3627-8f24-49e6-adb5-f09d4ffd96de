from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)

class StockMoveLine(models.Model):
    _inherit = 'stock.move.line'

    # 重写或创建 display_warning 字段
    display_warning = fields.Boolean(
        string="Display Warning",
        compute='_compute_display_warning',
        store=False,
        readonly=True,  # 设置为只读
        help="Transfer quantity is different from the LOT available quantity. Please DOUBLE check."
    )
    
    # 动态计算help信息的字段（可选，如果需要动态提示）
    display_warning_help = fields.Char(
        string="Warning Help",
        compute='_compute_display_warning',
        store=False
    )



    # 添加计算字段用于直接访问相关信息
    picking_partner_id = fields.Many2one(
        'res.partner',
        string="Partner",
        compute='_compute_picking_info',
        store=False
    )




    @api.depends('quantity', 'lot_qty_available', 'lot_id')
    def _compute_display_warning(self):
        """计算是否显示警告"""
        for line in self:
            if line.lot_id and line.quantity and line.lot_qty_available:
                # 允许一定的浮点数误差
                if abs(line.quantity - line.lot_qty_available) > 0.01:
                    line.display_warning = True
                    line.display_warning_help = "Transfer quantity is different from the LOT available quantity. Please DOUBLE check."
                else:
                    line.display_warning = False
                    line.display_warning_help = ""
            else:
                line.display_warning = False
                line.display_warning_help = ""

    @api.onchange('quant_id')
    def _onchange_quant_id(self):
        """当选择 quant 时，自动填充相关字段"""
        if self.quant_id:
            # 自动填充位置信息
            if self.quant_id.location_id:
                self.location_id = self.quant_id.location_id

            # 自动填充批次信息
            if self.quant_id.lot_id:
                self.lot_id = self.quant_id.lot_id
                self.lot_name = self.quant_id.lot_id.name

            # 自动填充产品信息
            if self.quant_id.product_id:
                self.product_id = self.quant_id.product_id

    @api.depends('picking_id', 'picking_id.partner_id')
    def _compute_picking_info(self):
        """计算picking相关信息"""
        for line in self:
            line.picking_partner_id = line.picking_id.partner_id if line.picking_id else False
