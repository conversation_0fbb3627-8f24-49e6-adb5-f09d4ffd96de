{
    "name": "Lot Cust - jsi",
    "summary": """
        This module will add below features:
            - Auto generate lot name in detailed operation
            - Set view level Decimal Precision
        """,
    "version": "17.0.1.0.0",
    "author": "Odoo PS",
    "license": "OEEL-1",
    "depends": [
        "stock",
        "purchase",
    ],
    "data": [
        "data/stock_move_server_action.xml",
        "views/stock_move_line.xml",
        "views/stock_production_lot.xml",
        "views/purchase_order.xml",
        "views/stock_quant.xml",
        "data/sequence_lot.xml",
        "security/ir.model.access.csv",
    ],
    "assets": {
        'web.assets_backend': [
            ('replace', 'stock/static/src/views/picking_form/stock_move_one2many.xml', 'lot_cust_jsi/static/src/views/picking_form/stock_move_one2many.xml'),
        ],
    },
    "post_init_hook": "_sequence_lot_name_date_range_post_init",
    # Only used to link to the analysis / Ps-tech store
    "task_id": [2509985, 3186292],
}
