<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="stock.MovesListRenderer.RecordRow" t-inherit-mode="primary" t-inherit="web.ListRenderer.RecordRow">
        <!--
            Client dont want to show the details button in the list view
            Many ways:
                We can simply use 'remove' in manifest to remove the file, but then there is a error can see if we do Inspect
                "The following modules are needed by other modules but have not been defined, they may not be present in the correct asset bundle ['@stock/views/picking_form/stock_move_one2many']"
                So plan is simply 'replace' with blank file, we will use this file in our manifest.
                Can use Xpath too but it will be very tricky and there is a clear way to do it so...
        -->
    </t>

</templates>
