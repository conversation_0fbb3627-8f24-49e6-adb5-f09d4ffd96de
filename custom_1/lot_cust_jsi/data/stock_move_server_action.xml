<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="action_reset_date_on_lot_name" model="ir.actions.server">
        <field name="name">Reset Date on Lot name</field>
        <field name="model_id" ref="stock.model_stock_move_line"/>
        <field name="binding_model_id" ref="stock.model_stock_move_line"/>
        <field name="state">code</field>
        <field name="code">records._action_reset_date_on_lot_name()</field>
    </record>
    <record id="action_reset_quantity" model="ir.actions.server">
        <field name="name">Reset Quantity to 0</field>
        <field name="model_id" ref="stock.model_stock_move_line"/>
        <field name="binding_model_id" ref="stock.model_stock_move_line"/>
        <field name="state">code</field>
        <field name="code">records._action_reset_quantity()</field>
    </record>
</odoo>
