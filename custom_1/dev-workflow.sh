#!/bin/bash

# Odoo 开发环境管理脚本
# 用于在完整开发环境和特定功能分支之间切换

set -e

INTEGRATION_BRANCH="develop-integration-panfu"

# 显示帮助信息
show_help() {
    echo "🔧 Odoo Development Workflow Helper"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  dev       - Switch to integration branch (complete development environment)"
    echo "  feature   - Switch to specific feature branch"
    echo "  status    - Show current branch and plugin status"
    echo "  help      - Show this help message"
    echo ""
    echo "📝 Workflow:"
    echo "1. Use 'dev' for daily development and testing (all plugins available)"
    echo "2. Use 'feature <branch-name>' to work on specific features"
    echo "3. Cherry-pick commits from integration branch to feature branches"
}

# 切换到开发集成分支
switch_to_dev() {
    echo "🚀 Switching to development integration branch..."
    
    if git show-ref --verify --quiet refs/heads/$INTEGRATION_BRANCH; then
        git checkout $INTEGRATION_BRANCH
        echo "✅ Switched to $INTEGRATION_BRANCH"
    else
        echo "❌ Integration branch not found."
        exit 1
    fi
    
    show_status
}

# 切换到特定功能分支
switch_to_feature() {
    if [ -z "$1" ]; then
        echo "📋 Available feature branches:"
        git branch -r | grep -E "(feature/)" | sed 's/origin\///' | sed 's/^/   - /'
        echo ""
        read -p "Enter feature branch name: " branch_name
    else
        branch_name="$1"
    fi
    
    echo "🔀 Switching to feature branch: $branch_name"
    git checkout "$branch_name" || {
        echo "❌ Failed to switch to $branch_name"
        exit 1
    }
    
    show_status
}

# 显示状态
show_status() {
    echo ""
    echo "🔧 Current branch: $(git branch --show-current)"
    echo "📦 Available panfu plugins:"
    ls -1 | grep "_panfu$" | while read plugin; do
        if [ -f "$plugin/__manifest__.py" ]; then
            echo "   ✅ $plugin (complete)"
        else
            echo "   ❌ $plugin (incomplete)"
        fi
    done
    echo ""
}

# 主逻辑
case "${1:-help}" in
    "dev")
        switch_to_dev
        ;;
    "feature")
        switch_to_feature "$2"
        ;;
    "status")
        show_status
        ;;
    "help"|*)
        show_help
        ;;
esac
