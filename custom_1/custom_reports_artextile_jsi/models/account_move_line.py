from odoo import models, fields


class AccountMoveLine(models.Model):
    _inherit = "account.move.line"

    communication_ar = fields.Char(string="Communication", compute="_compute_communication_ar")

    def _compute_communication_ar(self):
        """
            Communication column for Followup report
        """
        for line in self:
            line.communication_ar = '%s-%s' % (line.move_name or '', line.invoice_origin or '')
