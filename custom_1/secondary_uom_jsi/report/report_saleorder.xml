<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <template id="report_saleorder_document_secondary_uom_jsi" inherit_id="sale.report_saleorder_document">
            <th name="th_quantity" position="before">
                <th class="text-end">Secondary Qty</th>
            </th>
            <td name="td_quantity" position="before">
                <td class="text-end">
                    <span t-field="line.secondary_uom_qty" />
                    <span t-field="line.secondary_uom_id" />
                </td>
            </td>
        </template>
    </data>
</odoo>
