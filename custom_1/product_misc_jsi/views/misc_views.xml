<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <record id="view_tree_product_brand_product_misc_jsi" model="ir.ui.view">
        <field name="name">view.tree.product.brand.product.misc.jsi</field>
        <field name="model">product.brand</field>
        <field name="priority">16</field>
        <field name="arch" type="xml">
            <tree editable="top">
                <field name="sequence" widget="handle" />
                <field name="name" />
                <field name="brand_code" />
                <field name="brand_full_name" />
            </tree>
        </field>
    </record>
    <record id="action_product_brand_product_misc_jsi" model="ir.actions.act_window">
        <field name="name">Brand</field>
        <field name="res_model">product.brand</field>
        <field name="view_mode">tree</field>
    </record>
    <menuitem
        name="Brand"
        action="action_product_brand_product_misc_jsi"
        id="menu_action_product_brand_product_misc_jsi"
        parent="stock.menu_stock_inventory_control"
    />
    <record id="view_tree_product_subcategory_product_misc_jsi" model="ir.ui.view">
        <field name="name">view.tree.product.subcategory.product.misc.jsi</field>
        <field name="model">product.subcategory</field>
        <field name="priority">16</field>
        <field name="arch" type="xml">
            <tree editable="top">
                <field name="name" />
                <field name="active" widget="boolean_toggle" />
            </tree>
        </field>
    </record>
    <record id="action_product_subcategory_product_misc_jsi" model="ir.actions.act_window">
        <field name="name">Sub Category</field>
        <field name="res_model">product.subcategory</field>
        <field name="view_mode">tree</field>
    </record>
    <menuitem
        name="Sub Category"
        action="action_product_subcategory_product_misc_jsi"
        id="menu_action_product_subcategory_product_misc_jsi"
        parent="stock.menu_stock_inventory_control"
    />
    <record id="view_tree_product_pattern_product_misc_jsi" model="ir.ui.view">
        <field name="name">view.tree.product.pattern.product.misc.jsi</field>
        <field name="model">product.pattern</field>
        <field name="priority">16</field>
        <field name="arch" type="xml">
            <tree editable="top">
                <field name="name" />
                <field name="active" widget="boolean_toggle" />
            </tree>
        </field>
    </record>
    <record id="action_product_pattern_product_misc_jsi" model="ir.actions.act_window">
        <field name="name">Pattern</field>
        <field name="res_model">product.pattern</field>
        <field name="view_mode">tree</field>
    </record>
    <menuitem
        name="Pattern"
        action="action_product_pattern_product_misc_jsi"
        id="menu_product_pattern_product_misc_jsi"
        parent="stock.menu_stock_inventory_control"
    />
    <record id="view_tree_product_colour_product_misc_jsi" model="ir.ui.view">
        <field name="name">view.tree.product.colour.product.misc.jsi</field>
        <field name="model">product.colour</field>
        <field name="priority">16</field>
        <field name="arch" type="xml">
            <tree editable="top">
                <field name="sequence" widget="handle" />
                <field name="name" />
                <field name="group" />
                <field name="active" widget="boolean_toggel"/>
            </tree>
        </field>
    </record>
    <record id="action_product_colour_product_misc_jsi" model="ir.actions.act_window">
        <field name="name">Colour</field>
        <field name="res_model">product.colour</field>
        <field name="view_mode">tree</field>
    </record>
    <menuitem
        name="Colour"
        action="action_product_colour_product_misc_jsi"
        id="menu_product_colour_product_misc_jsi"
        parent="stock.menu_stock_inventory_control"
    />
    <record id="view_tree_product_yarn_count_product_misc_jsi" model="ir.ui.view">
        <field name="name">view.tree.product.yarn.count.product.misc.jsi</field>
        <field name="model">product.yarn.count</field>
        <field name="priority">16</field>
        <field name="arch" type="xml">
            <tree editable="top">
                <field name="sequence" widget="handle" />
                <field name="name" />
                <field name="yarn_count" />
                <field name="type" />
            </tree>
        </field>
    </record>
    <record id="action_product_yarn_count_product_misc_jsi" model="ir.actions.act_window">
        <field name="name">Yarn Count</field>
        <field name="res_model">product.yarn.count</field>
        <field name="view_mode">tree</field>
    </record>
    <menuitem
        name="Yarn Count"
        action="action_product_yarn_count_product_misc_jsi"
        id="menu_product_yarn_count_product_misc_jsi"
        parent="stock.menu_stock_inventory_control"
    />
    <record id="view_tree_product_composition_product_misc_jsi" model="ir.ui.view">
        <field name="name">view.tree.product.composition.product.misc.jsi</field>
        <field name="model">product.composition</field>
        <field name="priority">16</field>
        <field name="arch" type="xml">
            <tree editable="top">
                <field name="sequence" widget="handle" />
                <field name="name" />
                <field name="abbr" />
                <field name="group" />
            </tree>
        </field>
    </record>
    <record id="action_product_composition_product_misc_jsi" model="ir.actions.act_window">
        <field name="name">Composition</field>
        <field name="res_model">product.composition</field>
        <field name="view_mode">tree</field>
    </record>
    <menuitem
        name="Composition"
        action="action_product_composition_product_misc_jsi"
        id="menu_product_composition_product_misc_jsi"
        parent="stock.menu_stock_inventory_control"
    />
    <record id="view_form_product_collection_product_misc_jsi" model="ir.ui.view">
        <field name="name">view.form.product.collection.product.misc.jsi</field>
        <field name="model">product.collection</field>
        <field name="arch" type="xml">
            <form>
                <header />
                <sheet string="Collection">
                    <widget
                        name="web_ribbon"
                        text="Archived"
                        bg_color="bg-danger"
                        invisible="active"
                    />
                    <field name="active" invisible="1" />
                    <div class="oe_title">
                        <h1>
                            <field name="name" required="1" placeholder="Name..." />
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="code" />
                            <field name="composition" />
                            <field name="name_full" />
                            <field name="other_spec" />
                            <field name="pattern_colour" />
                            <field name="price_range" />
                        </group>
                        <group>
                            <field name="season" />
                            <field name="super_level" />
                            <field name="weight_range" />
                            <field name="sequence" />
                            <field name="brand" />
                            <field name="region_of_origin" />
                            <field name="description" />
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>
    <record id="view_tree_product_collection_product_misc_jsi" model="ir.ui.view">
        <field name="name">view.tree.product.collection.product.misc.jsi</field>
        <field name="model">product.collection</field>
        <field name="priority">16</field>
        <field name="arch" type="xml">
            <tree>
                <field name="sequence" widget="handle" />
                <field name="name" />
                <field name="name_full" />
                <field name="code" />
                <field name="brand" />
                <field name="composition" />
                <field name="weight_range" />
                <field name="price_range" />
                <field name="season" />
            </tree>
        </field>
    </record>
    <record id="action_product_collection_product_misc_jsi" model="ir.actions.act_window">
        <field name="name">Collection</field>
        <field name="res_model">product.collection</field>
        <field name="view_mode">tree,form</field>
    </record>
    <menuitem
        name="Collection"
        action="action_product_collection_product_misc_jsi"
        id="menu_product_collection_product_misc_jsi"
        parent="stock.menu_stock_inventory_control"
    />
</odoo>
