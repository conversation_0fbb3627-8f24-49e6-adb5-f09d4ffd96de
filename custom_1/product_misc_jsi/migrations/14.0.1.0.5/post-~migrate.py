from odoo.upgrade import util


def migrate(cr, version):

    cr.execute("SELECT latest_version FROM ir_module_module WHERE name='base'")
    util.ENVIRON["__base_version"] = util.parse_version(cr.fetchone()[0])

    # remove view
    util.remove_view(cr, "product_misc_jsi.view_product_template_tree_editable_product_misc_jsi")

    # remove window action
    util.remove_record(cr, "product_misc_jsi.action_product_template_tree_editable_product_misc_jsi")
    util.remove_record(cr, "product_misc_jsi.action_view_product_template_tree_editable_product_misc_jsi")

    # remove menu
    util.remove_record(cr, "product_misc_jsi.menu_product_template_tree_editable_product_misc_jsi")
