# Field Creation Restriction - 技术方案设计

## 技术架构与实现思路

- 通过继承和扩展 Odoo 相关模型和视图，针对关键的 many2one 字段（Master Product、Vendor、供应商信息等），禁用字段的自动创建功能。
- 在 XML 视图中为目标字段设置 options="{'no_create': True, 'no_create_edit': True}"，禁用"创建并编辑"功能。
- 对于树视图，设置 create="false" 属性禁用新建按钮。
- 确保覆盖 Studio 自定义视图，保持配置的一致性。
- 兼容多语言提示，确保用户体验。

## 涉及模块
- 产品（product.product 或相关自定义字段）
- 采购（purchase.order 或相关 Vendor 字段）

## 技术选型
- Odoo 17 原生继承机制
- 仅后端 XML/py，无需前端 JS（如有特殊小部件再补充）

## 数据库/接口
- 无需新增表，仅扩展视图和模型

## 测试策略
- 单元测试：尝试输入不存在的 Master Product/Vendor，验证无法创建
- 界面测试：验证下拉选择器无“创建并编辑”入口
- 多语言测试：提示信息友好

```mermaid
graph TD;
    用户--->界面下拉选择器--->仅可选已有记录
    用户--X--->自动创建新记录
``` 