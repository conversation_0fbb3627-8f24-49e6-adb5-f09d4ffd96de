# Field Creation Restriction - 需求文档

## 介绍

为防止用户在关键字段中误操作或随意创建新记录，需要在 Odoo 系统中限制特定字段的自动创建功能。该插件通过禁用 many2one 字段的"输入新内容自动创建记录"功能，确保数据规范性和完整性。主要涵盖产品界面的 Master Product 字段、采购界面的 Vendor 字段以及供应商信息相关字段。

## 需求

### 需求 1 - 限制字段自动创建功能

**用户故事：**
作为一名业务用户，我希望在关键的 many2one 字段（如 Master Product、Vendor 等）中只能选择已有记录，不能通过输入新内容自动创建新记录，以保证数据规范性和唯一性。

#### 验收标准

1. While 用户在产品界面编辑 Master Product 字段时，the 系统 shall 仅允许选择已有 Master Product，不允许通过输入新内容自动创建新 Master Product。
2. While 用户在采购界面编辑 Vendor 字段时，the 系统 shall 仅允许选择已有 Vendor，不允许通过输入新内容自动创建新 Vendor。
3. While 用户在供应商信息界面编辑相关字段时，the 系统 shall 仅允许选择已有记录，不允许自动创建新记录。
4. When 用户尝试输入不存在的内容时，the 系统 shall 给出友好提示，禁止自动创建。
