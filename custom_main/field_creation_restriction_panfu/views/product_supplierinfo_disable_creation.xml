<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 继承基础的 product.supplierinfo 树视图 -->
    <record id="product_supplierinfo_tree_field_creation_restriction_panfu" model="ir.ui.view">
        <field name="name">product.supplierinfo.tree.field.creation.restriction.panfu</field>
        <field name="model">product.supplierinfo</field>
        <field name="inherit_id" ref="product.product_supplierinfo_tree_view"/>
        <field name="arch" type="xml">
            <tree position="attributes">
                <attribute name="create">false</attribute>
            </tree>
        </field>
    </record>

    <!-- 继承 purchase 模块的 product.supplierinfo 树视图2 -->
    <record id="product_supplierinfo_tree_view2_field_creation_restriction_panfu" model="ir.ui.view">
        <field name="name">product.supplierinfo.tree.view2.field.creation.restriction.panfu</field>
        <field name="model">product.supplierinfo</field>
        <field name="inherit_id" ref="purchase.product_supplierinfo_tree_view2"/>
        <field name="arch" type="xml">
            <tree position="attributes">
                <attribute name="create">false</attribute>
            </tree>
        </field>
    </record>
</odoo>
