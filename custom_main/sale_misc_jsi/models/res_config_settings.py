from odoo import fields, models


class ResConfigSettings(models.TransientModel):
    _inherit = "res.config.settings"

    use_so_qty_limit = fields.Boolean(
        related="company_id.use_so_qty_limit", string="Activate SO Quantity Limit", readonly=False
    )
    so_qty_limit = fields.Float(related="company_id.so_qty_limit", string="SO Quantity Limit", readonly=False)
    use_so_days_limit = fields.Boolean(
        related="company_id.use_so_days_limit", string="Activate SO Days Limit", readonly=False
    )
    so_days_limit = fields.Float(related="company_id.so_days_limit", string="SO Days Limit", readonly=False)
