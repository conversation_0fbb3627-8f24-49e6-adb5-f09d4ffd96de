msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 14.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-05-25 13:42+0000\n"
"PO-Revision-Date: 2021-05-25 12:18+0000\n"
"Last-Translator: nni <<EMAIL>>, 2022\n"
"Language-Team: Chinese (China) (https://www.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: custom_reports_artextile_jsi
#: code:custom_reports_artextile_jsi/wizard/partner_followup.py:0
#, python-format
msgid "FOLLOWUP REPORT"
msgstr "对账报告"

#. module: custom_reports_artextile_jsi
#: code:custom_reports_artextile_jsi/wizard/partner_followup.py:0
#, python-format
msgid "Customer"
msgstr "客户名称"

#. module: custom_reports_artextile_jsi
#: code:custom_reports_artextile_jsi/wizard/partner_followup.py:0
#, python-format
msgid "Customer Code"
msgstr "客户编号"

#. module: custom_reports_artextile_jsi
#: code:custom_reports_artextile_jsi/wizard/partner_followup.py:0
#, python-format
msgid "Report Date"
msgstr "报告日期"

#. module: custom_reports_artextile_jsi
#: code:custom_reports_artextile_jsi/wizard/partner_followup.py:0
#, python-format
msgid "Journal No."
msgstr "凭证号"

#. module: custom_reports_artextile_jsi
#: code:custom_reports_artextile_jsi/wizard/partner_followup.py:0
#, python-format
msgid "Date"
msgstr "日期"

#. module: custom_reports_artextile_jsi
#: code:custom_reports_artextile_jsi/wizard/partner_followup.py:0
#, python-format
msgid "Due Date"
msgstr "到期日期"

#. module: custom_reports_artextile_jsi
#: code:custom_reports_artextile_jsi/wizard/partner_followup.py:0
#, python-format
msgid "Source Document"
msgstr "源文档号"

#. module: custom_reports_artextile_jsi
#: code:custom_reports_artextile_jsi/wizard/partner_followup.py:0
#, python-format
msgid "Amount"
msgstr "金额"

#. module: custom_reports_artextile_jsi
#: code:custom_reports_artextile_jsi/wizard/partner_followup.py:0
#, python-format
msgid "Due amount"
msgstr "结余金额"

#. module: custom_reports_artextile_jsi
#: code:custom_reports_artextile_jsi/wizard/partner_followup.py:0
#, python-format
msgid "Total Due"
msgstr "合计"

#. module: custom_reports_artextile_jsi
#: code:custom_reports_artextile_jsi/wizard/partner_followup.py:0
#, python-format
msgid "Total Overdue"
msgstr "逾期金额"
