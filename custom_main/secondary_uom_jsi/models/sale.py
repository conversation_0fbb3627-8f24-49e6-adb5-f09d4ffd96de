from odoo import api, fields, models


class SaleOrder(models.Model):
    _inherit = "sale.order"

    total_weight = fields.Float("Total Weight", compute="_compute_all_weight")
    courier_tracking = fields.Char("Courier Tracking")

    @api.depends("order_line.product_id.weight", "order_line.product_uom_qty", "order_line.product_uom")
    def _compute_all_weight(self):
        uom_yard = self.env.ref("secondary_uom_jsi.product_uom_yard")
        uom_meter = self.env.ref("uom.product_uom_meter")
        for sale in self:
            total_weight = 0.0
            # UOM meter
            if sale.order_line and sale.order_line[0].product_uom.id != uom_yard.id:
                total_weight = sum(line.product_id.weight * line.product_uom_qty for line in sale.order_line) * 1.1
            # UOM yard
            if sale.order_line and sale.order_line[0].product_uom.id == uom_yard.id:
                for line in sale.order_line:
                    qty_meter = uom_yard._compute_quantity(line.product_uom_qty, uom_meter, rounding_method="HALF-UP")
                    total_weight += line.product_id.weight * qty_meter
                total_weight = total_weight * 1.1

            sale.total_weight = total_weight


class SaleOrderLine(models.Model):
    _inherit = "sale.order.line"

    secondary_uom_qty = fields.Float("Secondary qty", digits="Product Unit of Measure", compute="_compute_product_uom_qty", store=True, readonly=False, precompute=True)
    secondary_uom_id = fields.Many2one("uom.uom", "Secondary uom", compute="_compute_product_uom_qty", store=True, readonly=False, precompute=True)
    secondary_uom_list_price = fields.Float("Secondary Sales Price", digits="Product Price", compute="_compute_product_uom_qty", store=True, readonly=False, precompute=True)

    @api.depends('secondary_uom_list_price')
    def _compute_price_unit(self):
        super(SaleOrderLine, self)._compute_price_unit()
        for line in self:
            if line.secondary_uom_id and line.secondary_uom_id != line.product_uom:
                line.price_unit = line.secondary_uom_id._compute_price(line.secondary_uom_list_price, line.product_uom)
            else:
                line.price_unit = line.secondary_uom_list_price

    def _compute_product_uom_qty(self):
        super(SaleOrderLine, self)._compute_product_uom_qty()
        for line in self:
            if line.product_id:
                line.secondary_uom_id = line.product_id.secondary_uom_id.id
            if line.order_id.pricelist_id and line.order_partner_id:
                line.secondary_uom_list_price = line.product_id.secondary_uom_list_price
            if line.secondary_uom_id and line.secondary_uom_id != line.product_uom:
                line.secondary_uom_qty = line.product_uom._compute_quantity(
                    line.product_uom_qty, line.secondary_uom_id, rounding_method="HALF-UP"
                )
            else:
                line.secondary_uom_qty = line.product_uom_qty

    def _prepare_invoice_line(self, **optional_values):
        res = super(SaleOrderLine, self)._prepare_invoice_line(**optional_values)
        res.update({
            "secondary_uom_id": self.secondary_uom_id.id,
            "secondary_uom_qty": self.secondary_uom_qty,
            "secondary_uom_list_price": self.secondary_uom_list_price,
            "quantity": self.product_uom_qty,
            })
        return res
