<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <record id="view_order_form_secondary_uom_jsi" model="ir.ui.view">
            <field name="name">sale.order.form.multi.uom.jsi</field>
            <field name="model">sale.order</field>
            <field name="inherit_id" ref="sale.view_order_form" />
            <field name="arch" type="xml">
                <xpath expr="//field[@name='sale_order_template_id']" position="after">
                    <field name="total_weight" />
                    <field name="courier_tracking" />
                </xpath>
                <xpath expr="//tree/field[@name='product_uom_qty']" position="before">
                    <field name="secondary_uom_qty" optional="show" readonly="1" force_save="True" digits="[12, 3]" />
                    <field name="secondary_uom_id" optional="show" readonly="1" force_save="True" />
                </xpath>
                <xpath expr="//tree/field[@name='price_unit']" position="before">
                    <field name="secondary_uom_list_price" optional="show" />
                </xpath>
                <xpath expr="//tree/field[@name='price_unit']" position="attributes">
                    <attribute name="optional">show</attribute>
                </xpath>
                <xpath expr="//tree/field[@name='product_uom_qty']" position="attributes">
                    <attribute name="optional">show</attribute>
                    <attribute name="digits">[12, 3]</attribute>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
