<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <record id="view_move_form_secondary_uom_jsi" model="ir.ui.view">
            <field name="name">account.move.form.multi.uom.jsi</field>
            <field name="model">account.move</field>
            <field name="inherit_id" ref="account.view_move_form" />
            <field name="arch" type="xml">
                <xpath expr="//field[@name='invoice_line_ids']/tree/field[@name='quantity']" position="before">
                    <field name="secondary_uom_qty" optional="show" readonly="1" force_save="True" />
                    <field name="secondary_uom_id" optional="show" readonly="1" force_save="True" />
                    <field name="secondary_uom_list_price" optional="show" />
                </xpath>
                <xpath expr="//field[@name='line_ids']//tree" position="inside">
                    <field name="secondary_uom_qty" invisible="1" />
                    <field name="secondary_uom_id" invisible="1" />
                    <field name="secondary_uom_list_price" invisible="1" />
                </xpath>
                <xpath expr="//field[@name='invoice_line_ids']//tree/field[@name='price_unit']" position="attributes">
                    <attribute name="optional">show</attribute>
                </xpath>
                <xpath expr="//field[@name='invoice_line_ids']//tree/field[@name='quantity']" position="attributes">
                    <attribute name="optional">show</attribute>
                </xpath>
            </field>
        </record>
    </data>
</odoo>
