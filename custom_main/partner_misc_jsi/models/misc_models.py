from odoo import fields, models


class CompanyGroup(models.Model):
    _name = "company.group"
    _description = "Company Group"

    name = fields.Char()
    description = fields.Text()


class SubSales(models.Model):
    _name = "sub.sales"
    _description = "Sub Sales"

    name = fields.Char()
    user_id = fields.Many2one("res.users", "Salesperson")


class PartnerMarket(models.Model):
    _name = "partner.market"
    _description = "Partner Market"

    name = fields.Char()
    parent_id = fields.Many2one("partner.market", "Market Parent1")
    parent2_id = fields.Many2one("partner.market", "Market Parent2")


class PartnerPerfChart(models.Model):
    _name = "partner.perf.chart"
    _description = "Customer Performance"

    partner_id = fields.Many2one("res.partner")
    date_from = fields.Datetime("From", required=True)
    date_to = fields.Datetime("To", required=True)
    categ_ids = fields.Many2many("product.category", string="Categories")
    target = fields.Float("Target")
    actual_system = fields.Float("Actual System", compute="_compute_actual_data")
    manual_adj = fields.Float("Manual Adj.")
    actual_display = fields.Float("Actual Display", compute="_compute_actual_data")
    attainment_rate = fields.Float("Attainment Rate", compute="_compute_actual_data")
    message_display = fields.Html("Message", translate=True)
    internal_remark = fields.Char("Internal Remark")
    is_display = fields.Boolean("Is Display?")

    def _compute_actual_data(self):
        SaleOrderLine = self.env['sale.order.line']
        for rec in self:
            actual_system = actual_display = attainment_rate = 0
            """ Why not read_group or filtered?
            read_group: 1) There will be different date_from and date_to in each line, so need to search order lines per record
                        2) Lets we search without date, get all the ids per partner, we still need to search again with domain [order_date<date_from,...]
            filtered  : 1) As we need to load all the child categ id, we cannot use child_of
            """
            order_lines = SaleOrderLine.search([
                ('id', '=', rec.partner_id.sale_order_ids.order_line.ids),
                ('order_id.date_order', '>=', rec.date_from),
                ('order_id.date_order', '<', rec.date_to),
                ('product_id.categ_id', 'child_of', rec.categ_ids.ids),
                ('state', 'in', ['sale', 'done'])
            ])
            if order_lines:
                actual_system = sum(order_lines.mapped('untaxed_amount_invoiced'))
                actual_display = actual_system + rec.manual_adj
                if rec.target:
                    attainment_rate = (actual_display / rec.target) * 100
            rec.actual_system = actual_system
            rec.actual_display = actual_display
            rec.attainment_rate = attainment_rate
