from odoo import _, api, fields, models
from odoo.exceptions import ValidationError


class Partner(models.Model):
    _inherit = "res.partner"

    fax = fields.Char()
    code = fields.Char()
    courier_tracking = fields.Char()
    initial = fields.Char(size=1)
    short_name = fields.Char()
    contact = fields.Char("Contact Name2")
    full_name = fields.Char()
    date_join = fields.Date("Join Date", default=fields.Date.context_today)
    nursing_end_date = fields.Date()
    market_id = fields.Many2one("partner.market", "Market")
    company_group_id = fields.Many2one("company.group", "Company Group")
    subsales_id = fields.Many2one("sub.sales", "Sub Sales")
    initial_user_id = fields.Many2one("res.users", "Initial Salesperson")
    perf_chart_ids = fields.One2many("partner.perf.chart", "partner_id", string="Customer Perf Data")

    @api.constrains("name", "is_company")
    def _check_unique_company_contact(self):
        """
        Restrict duplicate name for Companies.
        """
        company_names = []
        for p in self:
            if p.is_company:
                company_names.append(p.name)
        if company_names:
            same_contacts = self.read_group(
                [("name", "in", company_names), ("is_company", "=", True), ("id", "not in", self.ids)],
                ["name"],
                ["name"],
            )
            if same_contacts:
                raise ValidationError(
                    _(
                        "Duplicated company name is not allowed.\nExisting Name(s): %s"
                        % ", ".join([p["name"] for p in same_contacts])
                    )
                )

    def _get_name(self):
        """
        Overriding to remove company name from it's contact
        """
        if "no_parent_name" in self._context:
            return "%s" % (self.name)
        return super()._get_name()

    @api.model_create_multi
    def create(self, vals_list):
        """
        Override to add sequence in Customer Code(only for the Customer)
        No need to generate it during import
        """
        if "import_file" not in self.env.context:
            search_partner_mode = self.env.context.get("res_partner_search_mode")
            is_customer = search_partner_mode == "customer"
            if is_customer:
                for vals in vals_list:
                    if vals.get("initial") and not vals.get("parent_id"):
                        vals["code"] = self.get_initial_sequence(vals["initial"].upper())
        return super().create(vals_list)

    def write(self, vals):
        """
        Override to update Code if initial change(only for the Customer).
        """
        res = super().write(vals)
        for partner in self:
            if partner.customer_rank and "initial" in vals and not (partner.parent_id or "parent_id" in vals):
                partner.code = partner.get_initial_sequence(partner.initial.upper())
        return res

    def get_initial_sequence(self, initial):
        initial_seq = self.env["ir.sequence"].search([("code", "=", initial)])
        if not initial_seq:
            initial_seq = initial_seq.create(
                {
                    "name": ("Initial: %s" % initial),
                    "padding": 4,
                    "code": initial,
                    "number_next": 1,
                    "number_increment": 1,
                }
            )
        return "%s%s" % (initial, initial_seq.next_by_id())
