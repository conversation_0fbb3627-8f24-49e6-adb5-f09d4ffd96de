from odoo import api, fields, models


class ProductCategory(models.Model):
    _inherit = "product.category"

    # With the simple SQL constraints we can not check case insensitive unique
    def init(self):
        self._cr.execute(
            """CREATE UNIQUE INDEX IF NOT EXISTS unique_categ_complete_name ON product_category(UPPER(complete_name))"""
        )

    is_development = fields.Boolean()
    photo1 = fields.Binary("Photo 1")
    photo2 = fields.Binary("Photo 2")
    date_start = fields.Date("Start")
    date_end = fields.Date("Estimated End")
    brand_id = fields.Many2one("product.brand", "Brand")
    collection_id = fields.Many2one("product.collection", "Collection")
    pattern_colour = fields.Char("Pattern/colour")
    super_level = fields.Char()
    composition = fields.Char()
    season = fields.Char()
    price_range = fields.Char()
    weight_range = fields.Char()
    other_spec = fields.Char()
    region_of_origin = fields.Many2one("res.country")
    description = fields.Text()

    @api.onchange("collection_id")
    def onchange_collection_id(self):
        for categ in self:
            if not categ.collection_id:
                continue
            collection = categ.collection_id
            categ.update(
                {
                    "pattern_colour": collection.pattern_colour,
                    "super_level": collection.super_level,
                    "composition": collection.composition,
                    "season": collection.season,
                    "price_range": collection.price_range,
                    "weight_range": collection.weight_range,
                    "other_spec": collection.other_spec,
                    "region_of_origin": collection.region_of_origin,
                    "description": collection.description,
                    "brand_id": collection.brand,
                }
            )
