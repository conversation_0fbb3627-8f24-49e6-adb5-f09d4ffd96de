<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <record id="mrp_bom_form_view_product_misc_jsi" model="ir.ui.view">
            <field name="name">mrp.bom.form.view.product.misc.jsi</field>
            <field name="model">mrp.bom</field>
            <field name="inherit_id" ref="mrp.mrp_bom_form_view" />
            <field name="arch" type="xml">
                <xpath expr="//sheet" position="before">
                    <header>
                        <button
                            name="button_add_new_master_product"
                            type="object"
                            string="Update Master Product"
                            class="btn-primary"
                        />
                    </header>
                </xpath>
            </field>
        </record>
        <record id="action_add_new_master_product_misc_jsi" model="ir.actions.server">
            <field name="name">Update Master Product</field>
            <field name="model_id" ref="mrp.model_mrp_bom" />
            <field name="binding_model_id" ref="mrp.model_mrp_bom" />
            <field name="binding_view_types">list</field>
            <field name="state">code</field>
            <field name="code">records.button_add_new_master_product()</field>
        </record>
    </data>
</odoo>
