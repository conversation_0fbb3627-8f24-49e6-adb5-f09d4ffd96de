# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* website_artextile_jsi
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0+e-20250615\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-10 06:09+0000\n"
"PO-Revision-Date: 2025-07-10 06:09+0000\n"
"Last-Translator: \n"
"Language-Team: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: \n"

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.payment_confirmation_status_website_artextile_jsi
msgid "(<u>details</u>)"
msgstr "(<u>明细</u>)"

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.product_price_website_artextile_jsi
msgid ", reference only)"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.checkout_layout_website_artextile_jsi
msgid "<b class=\"w-100\">Order summary</b>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.address_on_payment_website_artextile_jsi
msgid "<b>Shipping: </b>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.address_on_payment_website_artextile_jsi
msgid "<i class=\"fa fa-edit me-1\"/>Change"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.product_stock_status_ar
msgid ""
"<i class=\"fa fa-exclamation-triangle\" title=\"Sold Out\" role=\"img\" aria-label=\"Sold Out\"/>\n"
"                    <span>Sold Out</span>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.product_stock_status_ar
msgid ""
"<i class=\"fa fa-exclamation-triangle\" title=\"Warning\" role=\"img\" aria-"
"label=\"Warning\"/>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.res_config_settings_view_form_website_product_multi_uom
msgid ""
"<span class=\"fa fa-lg fa-building-o p-2\" title=\"Values set here are "
"company-specific.\" groups=\"base.group_multi_company\"/>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.address_kanban_website_artextile_jsi
msgid "<span>Address Label:</span>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.product_stock_status_ar
msgid "<span>Available</span>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.side_content_website_artextile_jsi
msgid "<span>Company Name: </span>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.checkout_layout_website_artextile_jsi
msgid "<span>Price</span>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.checkout_layout_website_artextile_jsi
msgid "<span>Product</span>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.checkout_layout_website_artextile_jsi
msgid "<span>Quantity</span>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.checkout_layout_website_artextile_jsi
msgid "<span>Subtotal</span>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.product_website_artextile_jsi
msgid "<strong>Collection:</strong>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.product_website_artextile_jsi
msgid "<strong>Composition:</strong>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.side_content_website_artextile_jsi
msgid "<strong>Email: </strong>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.product_website_artextile_jsi
msgid "<strong>Feature:</strong>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.portal_my_home_website_artextile_jsi
msgid "<strong>Remarks</strong>"
msgstr "<strong>备注</strong>"

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.product_website_artextile_jsi
msgid "<strong>Sample Book:</strong>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.product_website_artextile_jsi
msgid "<strong>Weight (gram/meter):</strong>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.product_website_artextile_jsi
msgid "<strong>Width (cm):</strong>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.product_website_artextile_jsi
msgid "<strong>Yarn Count/Fineness:</strong>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.product_website_artextile_jsi
msgid "<u>PRODUCT DETAILS</u>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.product_custom_text_website_artextile_jsi
msgid "<u>Terms and Conditions</u>"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.product_website_artextile_jsi
msgid "A short description that will also appear on documents."
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.portal_my_home_website_artextile_jsi
msgid "Actual (HKD)"
msgstr "已完成 (HKD)"

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.cart_lines_website_artextile_jsi
msgid "Add one"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.address_website_artextile_jsi
msgid "Address Label"
msgstr ""

#. module: website_artextile_jsi
#: model:ir.model.fields,field_description:website_artextile_jsi.field_res_company__allowed_max_sale_qty
msgid "Allowed Max Sale Qty"
msgstr ""

#. module: website_artextile_jsi
#. odoo-python
#: code:addons/website_artextile_jsi/models/res_config_settings.py:0
#, python-format
msgid "Allowed Max. Quantity should be positive (i.e. >0) !"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.portal_my_home_website_artextile_jsi
msgid "Attainment %"
msgstr "达成率%"

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.product_stock_status_ar
msgid "Available from"
msgstr ""

#. module: website_artextile_jsi
#: model:ir.model.fields,field_description:website_artextile_jsi.field_sale_order__cart_quantity
msgid "Cart Quantity"
msgstr "购物车数量"

#. module: website_artextile_jsi
#: model:ir.model,name:website_artextile_jsi.model_res_company
msgid "Companies"
msgstr "公司"

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.address_website_artextile_jsi
msgid "Company Name"
msgstr "公司名称"

#. module: website_artextile_jsi
#: model:ir.model,name:website_artextile_jsi.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: website_artextile_jsi
#. odoo-javascript
#: code:addons/website_artextile_jsi/static/src/delivery_lead_time_reminder_dialog/delivery_lead_time_reminder_dialog.js:0
#, python-format
msgid "Confirm"
msgstr ""

#. module: website_artextile_jsi
#: model:ir.model,name:website_artextile_jsi.model_res_partner
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.address_website_artextile_jsi
msgid "Contact"
msgstr "联系人"

#. module: website_artextile_jsi
#: model:ir.model.fields,field_description:website_artextile_jsi.field_payment_method__default_add_to_partner
msgid "Default Add to Partner"
msgstr ""

#. module: website_artextile_jsi
#: model:ir.model.fields,field_description:website_artextile_jsi.field_product_product__description
#: model:ir.model.fields,field_description:website_artextile_jsi.field_product_template__description
msgid "Description"
msgstr "说明"

#. module: website_artextile_jsi
#: model:ir.model.fields,field_description:website_artextile_jsi.field_payment_provider__display_balance
msgid "Display Balance?"
msgstr ""

#. module: website_artextile_jsi
#. odoo-javascript
#: code:addons/website_artextile_jsi/static/src/delivery_lead_time_reminder_dialog/delivery_lead_time_reminder_dialog.xml:0
#, python-format
msgid "Don’t Remind me again today"
msgstr "今天不再提示"

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.payment_confirmation_status_website_artextile_jsi
msgid "Initial Deposit Balance:"
msgstr "初始预存款余额:"

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.product_stock_status_ar
msgid "Low Stock"
msgstr ""

#. module: website_artextile_jsi
#: model:ir.model.fields,field_description:website_artextile_jsi.field_res_config_settings__allowed_max_sale_qty
msgid "Maximum Sale Quantity"
msgstr ""

#. module: website_artextile_jsi
#: model:ir.model.fields,field_description:website_artextile_jsi.field_payment_provider__message_insufficient
msgid "Message Insufficient"
msgstr ""

#. module: website_artextile_jsi
#: model:ir.model.fields,field_description:website_artextile_jsi.field_payment_provider__message_sufficient
msgid "Message Sufficient"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.payment_confirmation_status_website_artextile_jsi
msgid "Net Deposit Balance:"
msgstr "= 可用预存款余额:"

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.show_delivery_lead_time_reminder
msgid ""
"Notice: Your order includes items with different delivery lead times. The "
"order will be delivered when all items have arrived."
msgstr "注意：您的订单中包含发货时间不同的商品。订单将于所有商品到齐后一起发货。"

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.cart_lines_website_artextile_jsi
msgid "Order#"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.cart_lines_website_artextile_jsi
msgid "Other Remarks:"
msgstr ""

#. module: website_artextile_jsi
#: model:ir.model,name:website_artextile_jsi.model_payment_method
#: model:ir.model.fields,field_description:website_artextile_jsi.field_res_partner__payment_method_ids
#: model:ir.model.fields,field_description:website_artextile_jsi.field_res_users__payment_method_ids
msgid "Payment Method"
msgstr "支付图标"

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.view_partner_form_website_product_multi_uom
msgid "Payment Methods"
msgstr ""

#. module: website_artextile_jsi
#: model:ir.model,name:website_artextile_jsi.model_payment_provider
msgid "Payment Provider"
msgstr "支付提供商"

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.payment_confirmation_status_website_artextile_jsi
msgid "Pending Orders:"
msgstr "- 未结算订单金额:"

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.portal_my_home_website_artextile_jsi
msgid "Performance"
msgstr "订单进度"

#. module: website_artextile_jsi
#: model:ir.model,name:website_artextile_jsi.model_product_pricelist
msgid "Pricelist"
msgstr "价格表"

#. module: website_artextile_jsi
#: model:ir.model,name:website_artextile_jsi.model_product_template
msgid "Product"
msgstr "产品"

#. module: website_artextile_jsi
#: model:ir.model,name:website_artextile_jsi.model_product_product
msgid "Product Variant"
msgstr "产品变体"

#. module: website_artextile_jsi
#. odoo-javascript
#: code:addons/website_artextile_jsi/static/src/js/website_sale.js:0
#, python-format
msgid "Quantity Validation failed"
msgstr ""

#. module: website_artextile_jsi
#. odoo-javascript
#: code:addons/website_artextile_jsi/static/src/js/website_sale.js:0
#, python-format
msgid ""
"Reminder: This product has a different delivery time from item(s) in your "
"cart. You can either check out your current cart first, or WAIT FOR all "
"items to ship together. Confirm to add it now?"
msgstr "提醒：此商品的发货时间与您购物车中的不同。您可以先结算购物车的商品，或者等待所有商品到齐一起发货。确认现在添加吗？"

#. module: website_artextile_jsi
#. odoo-javascript
#: code:addons/website_artextile_jsi/static/src/js/website_sale.js:0
#, python-format
msgid ""
"Reminder: Your cart has items with different delivery times. Confirm to wait"
" for all items to ship together?"
msgstr "提醒：您的购物车中包含不同发货时间的商品。确认等待所有商品到齐一起发货吗？"

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.cart_lines_website_artextile_jsi
msgid "Remove"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.cart_lines_website_artextile_jsi
msgid "Remove from cart"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.cart_lines_website_artextile_jsi
msgid "Remove one"
msgstr ""

#. module: website_artextile_jsi
#: model:ir.model.fields,field_description:website_artextile_jsi.field_res_config_settings__use_allowed_max_sale_qty
msgid "Restrict sale quantity per line in website"
msgstr ""

#. module: website_artextile_jsi
#: model:ir.model,name:website_artextile_jsi.model_sale_order
msgid "Sales Order"
msgstr "销售订单"

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.cart_lines_website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.product_stock_status_ar
msgid "Shipping:"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.res_config_settings_view_form_website_product_multi_uom
msgid ""
"Show Sold out if on product.qty_total(Free Inv.) is less than the Stock "
"Quantity Threshold"
msgstr ""

#. module: website_artextile_jsi
#: model:ir.model.fields,field_description:website_artextile_jsi.field_res_company__stock_qty_threshold
msgid "Stock Qty Threshold"
msgstr ""

#. module: website_artextile_jsi
#: model:ir.model.fields,field_description:website_artextile_jsi.field_res_config_settings__stock_qty_threshold
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.res_config_settings_view_form_website_product_multi_uom
msgid "Stock Quantity Threshold"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.portal_my_home_website_artextile_jsi
msgid "Target (HKD)"
msgstr "目标额 (HKD)"

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.portal_my_home_website_artextile_jsi
msgid "The period of the target is:"
msgstr ""

#. module: website_artextile_jsi
#. odoo-python
#: code:addons/website_artextile_jsi/controllers/main.py:0
#, python-format
msgid ""
"This Shipping Address is already used in Sale Order. You can't edit it."
msgstr ""

#. module: website_artextile_jsi
#. odoo-javascript
#: code:addons/website_artextile_jsi/static/src/js/website_sale.js:0
#, python-format
msgid "To order more than %s of these items, please reach us."
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.cart_lines_website_artextile_jsi
msgid "UOM"
msgstr ""

#. module: website_artextile_jsi
#: model:ir.model.fields,field_description:website_artextile_jsi.field_res_company__use_allowed_max_sale_qty
msgid "Use Allowed Max Sale Qty"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.res_config_settings_view_form_website_product_multi_uom
msgid "Warn customer if entered quantity is morethan the allowed quantity"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.cart_lines_website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.checkout_layout_website_artextile_jsi
msgid "Warning"
msgstr ""

#. module: website_artextile_jsi
#. odoo-javascript
#: code:addons/website_artextile_jsi/static/src/js/website_sale.js:0
#, python-format
msgid "Wrong quantity! Please enter a multiple of 0.05 or 0.125"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.cart_lines_website_artextile_jsi
msgid "eg: customer order#"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.cart_lines_website_artextile_jsi
msgid "eg: match color of previous order?/label requirements/other remarks"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.cart_lines_website_artextile_jsi
msgid "remove"
msgstr ""

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.portal_my_home_website_artextile_jsi
msgid "日 day. <br/>"
msgstr "日 <br/>"

#. module: website_artextile_jsi
#: model_terms:ir.ui.view,arch_db:website_artextile_jsi.portal_my_home_website_artextile_jsi
msgid "日 to"
msgstr ""
