from odoo import api, fields, models


class Res<PERSON>artner(models.Model):
    _inherit = "res.partner"

    remaining_credit = fields.Monetary(compute="_compute_remaining_credit", compute_sudo=True)
    total_pending_sales_credit = fields.Monetary(string="Total pending Sales", compute="_compute_total_pending_sales_credit")
    credit = fields.Monetary(compute_sudo=True)
    debit = fields.Monetary(compute_sudo=True)

    @api.depends("sale_order_ids.amount_total", "sale_order_ids.invoice_status", "sale_order_ids.state")
    def _compute_total_pending_sales_credit(self):
        """
        By default, odoo will search sales order only for login user.
        We used self.sudo() to get all sale orders.
        """
        sale_order_groups = (
            self.env["sale.order"]
            .sudo()
            .read_group(
                domain=[
                    ("partner_id", "in", self.ids),
                    ("invoice_ids", "=", False),
                    ("state", "in", ["sent", "done", "sale"]),
                ],
                fields=["amount_total"],
                groupby=["partner_id"],
            )
        )
        mapped_data = {d["partner_id"][0]: d["amount_total"] for d in sale_order_groups}
        for partner in self:
            partner.total_pending_sales_credit = mapped_data.get(partner.id, 0)

    @api.depends("credit_limit", "unreconciled_aml_ids")
    def _compute_remaining_credit(self):
        for partner in self:
            partner.remaining_credit = partner.credit_limit - partner.credit - partner.total_pending_sales_credit
