from odoo import fields, models


class StockMove(models.Model):
    _inherit = "stock.move"

    shipping_sch_id = fields.Many2one(related="purchase_line_id.shipping_sch_id", store=True)
    ready_date = fields.Date(related="purchase_line_id.ready_date", store=True)


class StockMoveLine(models.Model):
    _inherit = "stock.move.line"

    shipping_sch_id = fields.Many2one(related="move_id.shipping_sch_id", store=True)
    ready_date = fields.Date(related="move_id.ready_date", store=True)
