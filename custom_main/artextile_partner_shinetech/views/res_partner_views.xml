<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <record model="ir.ui.view" id="partner_view_buttons">
            <field name="name">partner.view.buttons</field>
            <field name="model">res.partner</field>
            <field name="inherit_id" ref="base.view_partner_form" />
            <field name="priority" eval="13"/>
            <field name="arch" type="xml">
                <xpath expr="//button[@name='schedule_meeting']" position="replace">
                </xpath>
                <xpath expr="//button[@name='%(purchase_stock.action_purchase_vendor_delay_report)d']" position="replace">
                </xpath>
                <div name="button_box" position="inside">
                    <button type="object"
                        groups="account.group_account_invoice"
                        class="oe_stat_button"
                        id="ar_partner_ledger_button"
                        icon="fa-pencil-square-o"
                        name="open_ar_partner_ledger"
                        title="AR Partner Ledger"
                        context="{'default_partner_id': id}">
                        <div class="o_stat_info">
                            <span class="o_stat_text">AR Partner Ledger</span>
                        </div>
                    </button>
                    <button class="oe_stat_button" type="object"
                        name="schedule_meeting"
                        icon="fa-calendar"
                        context="{'partner_id': id, 'partner_name': name}">
                        <field string="Meetings" name="meeting_count" widget="statinfo"/>
                    </button>
                    <button class="oe_stat_button" name="%(purchase_stock.action_purchase_vendor_delay_report)d" type="action"
                        groups="purchase.group_purchase_user"
                        icon="fa-truck"
                        context="{'search_default_partner_id': id}">
                        <div class="o_form_field o_stat_info">
                            <div class="o_row" invisible="on_time_rate &lt; 0">
                                <span class="o_stat_value">
                                    <field string="On-time Rate" name="on_time_rate" widget="integer"/>
                                </span>
                                <span class="o_stat_value">%</span>
                            </div>
                            <div class="o_stat_value" invisible="on_time_rate &gt;= 0">
                                No data yet
                            </div>
                            <span class="o_stat_text">On-time Rate</span>
                        </div>
                    </button>
                </div>
            </field>
        </record>
    </data>
</odoo>