<?xml version='1.0' encoding='utf-8' ?>
<odoo>
    <data>
        <record id="view_production_lot_form_lot_cust_jsi" model="ir.ui.view">
            <field name="name">stock.production.lot.form.lot.cust.jsi</field>
            <field name="model">stock.lot</field>
            <field name="inherit_id" ref="stock.view_production_lot_form" />
            <field name="arch" type="xml">
                <field name="ref" position="before">
                    <field name="gross_unit" />
                </field>
                <field name="ref" position="after">
                    <field name="remarks" />
                </field>
            </field>
        </record>
        <record id="view_production_lot_tree_lot_cust_jsi" model="ir.ui.view">
            <field name="name">stock.production.lot.tree.lot.cust.jsi</field>
            <field name="model">stock.lot</field>
            <field name="inherit_id" ref="stock.view_production_lot_tree" />
            <field name="arch" type="xml">
                <field name="product_id" position="after">
                    <field name="gross_unit" readonly="1" />
                </field>
            </field>
        </record>
    </data>
</odoo>
