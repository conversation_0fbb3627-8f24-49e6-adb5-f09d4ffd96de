{"permissions": {"allow": ["Bash(find:*)", "Bash(ls:*)", "Bash(pg_isready:*)", "<PERSON><PERSON>(createdb:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cp:*)", "<PERSON><PERSON>(chmod:*)", "Bash(PGPASSWORD=\"atx@1788\" psql -h localhost -p 5432 -U odoo17 -d odoo_restored -c \"\\d res_users\")", "Bash(PGPASSWORD=\"atx@1788\" psql -h localhost -p 5432 -U odoo17 -d odoo_restored -c \"SELECT id, login, password, totp_secret FROM res_users WHERE login=''admin'' OR id=2;\")", "Bash(PGPASSWORD=\"atx@1788\" psql -h localhost -p 5432 -U odoo17 -d odoo_restored -c \"UPDATE res_users SET password=''admin'', totp_secret=NULL WHERE id=2;\")", "Bash(PGPASSWORD=\"atx@1788\" psql -h localhost -p 5432 -U odoo17 -d odoo_restored -c \"SELECT COUNT(*) FROM ir_attachment WHERE store_fname IS NOT NULL;\")", "Bash(PGPASSWORD=\"atx@1788\" psql -h localhost -p 5432 -U odoo17 -d odoo_restored -c \"SELECT store_fname, db_datas FROM ir_attachment WHERE store_fname IS NOT NULL AND res_model IN (''product.template'', ''product.product'', ''res.partner'') LIMIT 5;\")", "Bash(PGPASSWORD=\"atx@1788\" psql -h localhost -p 5432 -U odoo17 -d odoo_restored -c \"SELECT COUNT(*) FROM ir_attachment WHERE store_fname IS NOT NULL AND store_fname != '''';\")", "Bash(PGPASSWORD=\"atx@1788\" psql -h localhost -p 5432 -U odoo17 -d odoo_restored -c \"SELECT COUNT(*) FROM ir_attachment WHERE store_fname IS NOT NULL AND store_fname <> '''';\")", "Bash(PGPASSWORD=\"atx@1788\" psql -h localhost -p 5432 -U odoo17 -d odoo_restored -c \"SELECT store_fname FROM ir_attachment WHERE store_fname IS NOT NULL LIMIT 5;\")", "Bash(PGPASSWORD=\"atx@1788\" psql -h localhost -p 5432 -U odoo17 -d odoo_restored -c \"SELECT key, value FROM ir_config_parameter WHERE key LIKE ''%base.url%'' OR key LIKE ''%web.base.url%'';\")", "Bash(PGPASSWORD=\"atx@1788\" psql -h localhost -p 5432 -U odoo17 -d odoo_restored -c \"SELECT name, url FROM ir_attachment WHERE mimetype LIKE ''image%'' LIMIT 5;\")", "Bash(PGPASSWORD=\"atx@1788\" psql -h localhost -p 5432 -U odoo17 -d odoo_restored -c \"UPDATE ir_attachment SET store_fname = REGEXP_REPLACE(store_fname, ''^[a-f0-9]{2}/'', store_fname) WHERE store_fname IS NOT NULL;\")", "<PERSON><PERSON>(pkill:*)", "Bash(PGPASSWORD=\"atx@1788\" psql -h localhost -p 5432 -U odoo17 -d odoo_restored -c \"SELECT id, step_image, step_image_fname FROM onboarding_onboarding_step WHERE id = 6;\")", "Bash(PGPASSWORD=\"atx@1788\" psql -h localhost -p 5432 -U odoo17 -d odoo_restored -c \"\\d onboarding_onboarding_step\")", "Bash(PGPASSWORD=\"atx@1788\" psql -h localhost -p 5432 -U odoo17 -d odoo_restored -c \"SELECT column_name FROM information_schema.columns WHERE table_name = ''onboarding_onboarding_step'' AND column_name LIKE ''%image%'';\")", "Bash(PGPASSWORD=\"atx@1788\" psql -h localhost -p 5432 -U odoo17 -d odoo_restored -c \"SELECT id, step_image_filename, title FROM onboarding_onboarding_step WHERE id = 6;\")", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(python3:*)", "<PERSON><PERSON>(true)", "mcp__asana__asana_list_workspaces", "mcp__asana__asana_search_tasks", "mcp__asana__asana_get_task", "mcp__asana__asana_get_task_stories", "mcp__asana__asana_update_task", "mcp__asana__asana_create_subtask", "mcp__asana__asana_create_task_story", "Bash(rg:*)", "Bash(. /Users/<USER>/dev/atx/odooenv/bin/activate)", "<PERSON><PERSON>(python:*)", "Bash(. /Users/<USER>/dev/atx/venv/bin/activate)", "Bash(PYTHONPATH=/Users/<USER>/dev/atx python3 -c \"import sys; sys.path.append(''/Users/<USER>/dev/atx''); from odoo.cli import main; main()\" -c odoo.conf -u 1208337958369478_ui_customization_panfu --stop-after-init --log-level=error)", "Bash(PYTHONPATH=/Users/<USER>/dev/atx/odoo python3 odoo/odoo-bin -c odoo.conf -u 1208337958369478_ui_customization_panfu --stop-after-init --log-level=error --test-enable)", "Bash(PYTHONPATH=/Users/<USER>/dev/atx/odoo python3 odoo/odoo-bin -c odoo.conf -u 1208337958369478_ui_customization_panfu --stop-after-init --log-level=error --test-enable --http-port=8070)", "Bash(psql:*)", "Bash(PYTHONPATH=/Users/<USER>/dev/atx/odoo python3 odoo/odoo-bin shell -c odoo.conf --no-http)", "Bash(PYTHONPATH=/Users/<USER>/dev/atx/odoo python3 odoo/odoo-bin -c odoo.conf -u 1208337958369478_ui_customization_panfu --stop-after-init --log-level=error --database odoo_restored)", "Bash(PYTHONPATH=/Users/<USER>/dev/atx/odoo python3 odoo/odoo-bin -c odoo.conf -u 1208337958369478_ui_customization_panfu --stop-after-init --log-level=error --database odoo_restored --http-port=8071)", "Bash(PYTHONPATH=/Users/<USER>/dev/atx/odoo python3 odoo/odoo-bin -c odoo.conf -u 1208337958369478_ui_customization_panfu --stop-after-init --log-level=error --database odoo_restored --http-port=8072)", "Bash(PYTHONPATH=/Users/<USER>/dev/atx/odoo python3 odoo/odoo-bin -c odoo.conf -u 1208337958369478_ui_customization_panfu --stop-after-init --log-level=error --database odoo_restored --http-port=0)", "Bash(PYTHONPATH=/Users/<USER>/dev/atx/odoo python3 odoo/odoo-bin -c odoo.conf -u 1208337958369478_ui_customization_panfu --stop-after-init --log-level=error --http-port=8071)", "Bash(PYTHONPATH=/Users/<USER>/dev/atx/odoo python3 odoo/odoo-bin -c odoo.conf -i 1208337958369478_ui_customization_panfu --stop-after-init --log-level=error --http-port=8071)", "Bash(PYTHONPATH=/Users/<USER>/dev/atx/odoo python3 odoo/odoo-bin -c odoo.conf -d odoo_restored -u 1208337958369478_ui_customization_panfu --stop-after-init --log-level=error --http-port=8071 --no-database-list)", "Bash(PYTHONPATH=/Users/<USER>/dev/atx/odoo python3 odoo/odoo-bin -c odoo.conf -d odoo_restored -u 1208337958369478_ui_customization_panfu --stop-after-init --log-level=error --http-port=8071)", "Bash(PYTHONPATH=/Users/<USER>/dev/atx/odoo python3 odoo/odoo-bin -c odoo.conf -d odoo_restored -i 1208337958369478_ui_customization_panfu --stop-after-init --log-level=info --http-port=8071)", "mcp__browser-use__browser_navigate", "mcp__browser-use__browser_form_input_fill", "mcp__browser-use__browser_click", "mcp__browser-use__browser_get_markdown", "mcp__browser-use__browser_get_clickable_elements", "mcp__browser-use__browser_screenshot", "mcp__browser-use__browser_get_text", "mcp__browser-use__browser_evaluate", "mcp__playwright__browser_navigate", "mcp__playwright__browser_type", "mcp__playwright__browser_click", "mcp__playwright__browser_wait_for", "Bash(rm:*)", "Bash(brew services:*)", "Bash(pg_ctl:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "<PERSON><PERSON>(mv:*)", "Bash(grep:*)"], "deny": []}}