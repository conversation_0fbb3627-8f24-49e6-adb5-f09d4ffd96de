# Odoo 17 开发最佳实践指南

> 📋 **项目启动必读** - 每次启动新项目前请完整阅读此文档

## 🚀 项目启动检查清单

### 1. 环境准备
- [ ] 确认使用正确数据库：`artx_7`
- [ ] 确认测试环境URL：`http://localhost:8069/?debug=1`
- [ ] 确认登录凭据：`<EMAIL>/ZXCde321`
- [ ] 确认Odoo版本为17

### 2. 模块结构检查
- [ ] 创建标准模块目录结构
- [ ] 配置`__manifest__.py`中的依赖关系
- [ ] 设置`security/ir.model.access.csv`权限文件
- [ ] 创建`tests/`目录和测试文件

### 3. 代码规范检查
- [ ] 确认导入顺序符合规范
- [ ] 设置翻译方法(`_`)
- [ ] 配置.gitignore文件
- [ ] 创建README.md文件

---

## 📁 模块目录结构规范

```
your_module/
├── __init__.py                 # 模块初始化
├── __manifest__.py            # 模块配置
├── models/
│   ├── __init__.py
│   ├── *.py                   # 数据模型
├── views/
│   ├── *.xml                  # UI视图
├── security/
│   ├── ir.model.access.csv   # 访问权限
│   └── *.xml                 # 记录规则
├── data/
│   └── *.xml                 # 演示数据
├── static/
│   ├── src/
│   │   ├── js/               # JavaScript文件
│   │   ├── css/              # 样式文件
│   │   └── xml/              # QWeb模板
├── tests/
│   ├── __init__.py
│   └── test_*.py             # 测试文件
└── i18n/                     # 翻译文件
```

---

## 🐍 Python开发规范

### 1. 导入顺序
```python
# 1. Python标准库
import base64
import re
from datetime import datetime

# 2. Odoo核心
from odoo import Command, _, api, fields, models
from odoo.tools.safe_eval import safe_eval as eval

# 3. Odoo插件
from odoo.addons.web.controllers.main import login_redirect
```

### 2. 字典和列表操作
```python
# ✅ 好 - 字典创建
my_dict = {'foo': 3, 'bar': 4}

# ❌ 避免
my_dict = {}
my_dict['foo'] = 3
my_dict['bar'] = 4

# ✅ 好 - 字典更新
my_dict.update(foo=3, bar=4, baz=5)

# ✅ 好 - 列表复制
new_list = list(old_list)
new_dict = dict(my_dict)
```

### 3. ORM最佳实践
```python
# ✅ 使用ORM查询
records = self.search([
    ('state', '=', 'active'),
    ('date', '>=', fields.Date.today())
])

# ❌ 避免直接SQL
self.env.cr.execute('SELECT * FROM table WHERE ...')
```

### 4. 计算字段规范
```python
@api.depends('value', 'tax')
@api.depends_context('company_id')
def _compute_total(self):
    for record in self:
        record.total = record.value + record.tax
```

---

## 🧪 测试规范

### 1. 测试类结构
```python
from odoo.tests.common import TransactionCase
from odoo.tests import tagged

@tagged('post_install', '-at_install')
class ModuleTestCase(TransactionCase):
    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        # 创建测试数据
        cls.test_record = cls.env['model.name'].create({...})

    def test_business_logic(self):
        """测试业务逻辑"""
        result = self.test_record.do_something()
        self.assertEqual(result, expected_value)
```

### 2. 运行测试命令
```bash
# 运行所有测试
odoo-bin -i your_module --test-enable

# 运行特定测试文件
odoo-bin --test-file=addons/your_module/tests/test_file.py

# 运行特定测试标签
odoo-bin --test-tags=/your_module:TestClass.test_method
```

---

## 🎨 前端开发规范

### 1. JavaScript模块结构
```javascript
/** @odoo-module **/
import { registry } from "@web/core/registry";

export class MyComponent extends owl.Component {
    setup() {
        // 初始化代码
    }
}

MyComponent.template = "my_module.MyComponent";
```

### 2. SCSS命名规范 - BEM方法
```scss
.o_module_block {
    &__element {
        // 元素样式
    }
    
    &--modifier {
        // 修饰符样式
    }
}
```

### 3. QWeb模板规范
```xml
<templates>
    <t t-name="my_module.template_name">
        <div class="o_module_block">
            <span t-esc="value"/>
        </div>
    </t>
</templates>
```

---

## 🔐 安全规范

### 1. 访问权限配置
```csv
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_model_user,model.user,model_model,base.group_user,1,0,0,0
access_model_manager,model.manager,model_model,base.group_system,1,1,1,1
```

### 2. 记录规则
```xml
<record id="rule_user_own_records" model="ir.rule">
    <field name="name">用户只能看到自己的记录</field>
    <field name="model_id" ref="model_your_model"/>
    <field name="domain_force">[('user_id', '=', user.id)]</field>
    <field name="groups" eval="[(4, ref('base.group_user'))]"/>
</record>
```

---

## 🌐 国际化规范

### 1. 翻译方法使用
```python
from odoo import _

# ✅ 正确
message = _('Hello %s', username)

# ❌ 错误
message = _('Hello %s' % username)
message = _('Hello ' + username)
```

### 2. 字段翻译
```python
name = fields.Char(string=_('Display Name'), translate=True)
```

---

## 📊 性能优化规范

### 1. 批量操作
```python
# ✅ 批量创建
partners = self.env['res.partner'].create([
    {'name': 'Alice', 'email': '<EMAIL>'},
    {'name': 'Bob', 'email': '<EMAIL>'},
])

# ✅ 批量读取
partners.read(['name', 'email'])
```

### 2. 避免N+1查询
```python
# ✅ 使用预加载
orders = self.env['sale.order'].search([])
for order in orders.with_context(prefetch_fields=True):
    print(order.partner_id.name)
```

---

## 🔧 调试和开发工具

### 1. 调试模式
- 始终在URL中添加`?debug=1`进入调试模式
- 使用`?debug=assets`调试静态资源

### 2. 日志记录
```python
import logging
_logger = logging.getLogger(__name__)

_logger.info('信息日志')
_logger.warning('警告日志')
_logger.error('错误日志')
```

---

## 📋 模块配置模板

### __manifest__.py模板
```python
{
    'name': '模块名称',
    'version': '********.0',
    'category': '模块分类',
    'summary': '简要描述',
    'description': """
    详细描述
    """,
    'author': '作者名称',
    'website': 'https://example.com',
    'depends': [
        'base',
        'mail',
    ],
    'data': [
        'security/ir.model.access.csv',
        'security/security.xml',
        'views/model_views.xml',
        'data/demo_data.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'your_module/static/src/css/style.css',
            'your_module/static/src/js/component.js',
        ],
    },
    'installable': True,
    'application': True,
    'auto_install': False,
    'license': 'LGPL-3',
}
```

---

## 🎯 命名规范标准

### 插件目录命名规范
**格式：** `插件作用_作者_asana任务编号`
**示例：** `stock_move_line_ui_enhancement_panfu_1208337958369478`

### 命名组件说明
- **插件作用**：使用小写字母和下划线，描述功能
- **作者**：使用小写字母
- **asana任务编号**：直接使用Asana提供的任务ID

### 每次提交前检查
- [ ] 代码符合PEP 8规范
- [ ] 所有字符串使用翻译函数
- [ ] 权限设置完整
- [ ] 测试用例覆盖主要功能
- [ ] 文档字符串完整
- [ ] 无调试代码遗留
- [ ] 插件命名符合规范：插件作用_作者_asana任务编号

### 命名规范检查
- [ ] 模型名称使用点分命名：`model.name`
- [ ] 字段名称使用小写加下划线：`field_name`
- [ ] 类名使用驼峰式：`ModelName`
- [ ] 方法名使用小写加下划线：`method_name`
- [ ] 插件目录名符合规范

---

## 📚 常用命令速查

### 开发命令
```bash
# 基于项目配置运行开发服务器
pkill odoo;./odoo-bin -c odoo.conf --dev=all
```

### 前端资源缓存清理
```bash
# 完全清除前端缓存
pkill odoo
rm -rf /Volumes/lucky/atx/artx_1/filestore/*
rm -rf /Volumes/lucky/atx/artx_1/.local/share/Odoo/filestore/*

# 清除ir_attachment缓存
psql -h localhost -U odoo -d artx_7 -c "
DELETE FROM ir_attachment 
WHERE url LIKE '/web/assets/%' OR name LIKE '%assets_%';
"

# 重新生成资源
./odoo-bin -c odoo.conf --dev=all --stop-after-init -u web
```

### Git工作流
```bash
# 创建功能分支
git checkout -b feature/new-feature

# 提交规范
git commit -m "[ADD] module: 添加新功能描述"

# 推送分支
git push origin feature/new-feature
```

---

## 🚨 常见错误避免

### 1. 避免直接修改数据库
```python
# ❌ 错误
self.env.cr.execute("UPDATE table SET field = 'value'")

# ✅ 正确
records.write({'field': 'value'})
```

### 2. 避免循环中查询
```python
# ❌ 错误
for record in records:
    partner = self.env['res.partner'].browse(record.partner_id.id)

# ✅ 正确
partners = records.mapped('partner_id')
```

### 3. 避免硬编码ID
```python
# ❌ 错误
partner = self.env['res.partner'].browse(1)

# ✅ 正确
partner = self.env.ref('base.partner_admin')
```

---

*最后更新：2025-07-20*  
*版本：1.0*  
*适用范围：Odoo 17开发*