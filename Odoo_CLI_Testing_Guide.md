# Odoo CLI 测试流程标准

## 测试目的
标准化Odoo模块的安装和功能测试流程，确保模块质量和稳定性。

## 前置条件
1. Odoo源码已下载并配置
2. 配置文件 `odoo.conf` 已设置
3. Python环境已配置
4. 数据库已初始化

## 标准测试流程

### 1. 环境检查
```bash
# 检查Odoo可执行文件
ls -la /path/to/odoo/odoo-bin

# 检查Python路径
which python3

# 检查配置文件
ls -la odoo.conf
```

### 2. 模块安装测试
```bash
# 基本安装测试
PYTHONPATH=/path/to/odoo python3 odoo/odoo-bin \
  -c odoo.conf \
  -i module_name \
  --stop-after-init \
  --log-level=error

# 模块更新测试（已安装时）
PYTHONPATH=/path/to/odoo python3 odoo/odoo-bin \
  -c odoo.conf \
  -u module_name \
  --stop-after-init \
  --log-level=error
```

### 3. 功能测试
```bash
# 带测试框架运行
PYTHONPATH=/path/to/odoo python3 odoo/odoo-bin \
  -c odoo.conf \
  -u module_name \
  --stop-after-init \
  --test-enable \
  --log-level=info
```

### 4. 端口冲突处理
```bash
# 如果默认8069端口被占用，使用备用端口
PYTHONPATH=/path/to/odoo python3 odoo/odoo-bin \
  -c odoo.conf \
  -u module_name \
  --http-port=8070 \
  --stop-after-init

# 或使用随机端口
PYTHONPATH=/path/to/odoo python3 odoo/odoo-bin \
  -c odoo.conf \
  -u module_name \
  --http-port=0 \
  --stop-after-init
```

### 5. 日志级别设置
- `--log-level=error`: 仅显示错误
- `--log-level=warning`: 显示警告和错误
- `--log-level=info`: 显示基本信息
- `--log-level=debug`: 显示调试信息

### 6. 数据库操作
```bash
# 使用特定数据库
-d database_name

# 创建新数据库测试
-d test_db_name
```

## 测试检查清单

### ✅ 安装验证
- [ ] 模块无安装错误
- [ ] 依赖模块正确加载
- [ ] 数据库表正确创建
- [ ] 视图正确继承

### ✅ 功能验证
- [ ] 模型字段正确创建
- [ ] 视图字段正确显示
- [ ] 权限设置正确
- [ ] 菜单项正确添加

### ✅ 回归测试
- [ ] 不破坏现有功能
- [ ] 不影响其他模块
- [ ] 性能无显著下降

## 常见问题处理

### 端口冲突
```bash
# 查找占用端口的进程
lsof -i :8069
# 或
netstat -tulpn | grep 8069
```

### 权限问题
```bash
# 检查文件权限
ls -la /path/to/odoo/
# 确保有执行权限
chmod +x odoo/odoo-bin
```

### 依赖问题
```bash
# 检查Python依赖
pip list | grep -i odoo
# 安装缺失依赖
pip install -r requirements.txt
```

## 测试记录模板

每次测试后记录：
- 测试时间：YYYY-MM-DD HH:MM
- 测试模块：module_name
- 测试类型：安装/功能/回归
- 测试结果：通过/失败
- 错误信息：如有错误请记录完整日志
- 备注：特殊说明

## 实际应用示例

### 示例：测试当前模块
```bash
# 路径：/Users/<USER>/dev/atx
PYTHONPATH=/Users/<USER>/dev/atx/odoo python3 odoo/odoo-bin \
  -c odoo.conf \
  -u 1208337958369478_ui_customization_panfu \
  --stop-after-init \
  --log-level=error \
  --test-enable \
  --http-port=8070
```

### 快速验证脚本
```bash
#!/bin/bash
# save as test_module.sh
MODULE_NAME=$1
PORT=${2:-8070}

echo "测试模块: $MODULE_NAME"
echo "使用端口: $PORT"

PYTHONPATH=/Users/<USER>/dev/atx/odoo python3 odoo/odoo-bin \
  -c odoo.conf \
  -u "$MODULE_NAME" \
  --stop-after-init \
  --log-level=error \
  --test-enable \
  --http-port="$PORT"

echo "测试完成"
```

使用方法：
```bash
chmod +x test_module.sh
./test_module.sh 1208337958369478_ui_customization_panfu 8070
```