#!/usr/bin/env python3

import sys
import os
sys.path.insert(0, '/Users/<USER>/dev/atx/odoo')

import odoo
from odoo.api import Environment
from odoo.modules.registry import Registry

# 配置
config_path = '/Users/<USER>/dev/atx/odoo.conf'
odoo.tools.config.parse_config(['-c', config_path])

# 获取数据库名称
db_name = odoo.tools.config['db_name'] or 'atx'

print(f"连接到数据库: {db_name}")

# 创建注册表
with Registry(db_name).cursor() as cr:
    env = Environment(cr, odoo.SUPERUSER_ID, {})
    
    # 检查stock.picking模型
    picking_model = env['ir.model'].search([('model', '=', 'stock.picking')])
    if picking_model:
        print(f"\n=== stock.picking 模型信息 ===")
        print(f"模型ID: {picking_model.id}")
        
        # 检查note字段
        note_field = env['ir.model.fields'].search([
            ('model_id', '=', picking_model.id),
            ('name', '=', 'note')
        ])
        
        if note_field:
            print(f"note字段信息:")
            print(f"  - 名称: {note_field.name}")
            print(f"  - 类型: {note_field.ttype}")
            print(f"  - 关联: {note_field.related}")
            print(f"  - 描述: {note_field.field_description}")
        else:
            print("note字段不存在")
    
    # 检查stock.move.line模型
    move_line_model = env['ir.model'].search([('model', '=', 'stock.move.line')])
    if move_line_model:
        print(f"\n=== stock.move.line 模型信息 ===")
        
        # 检查所有相关字段
        fields = env['ir.model.fields'].search([
            ('model_id', '=', move_line_model.id),
            ('name', 'in', ['po_description', 'vendor_item_no', 'shipping_schedule', 'eta', 'exmill_date', 'updated_exmill'])
        ])
        
        for field in fields:
            print(f"{field.name}:")
            print(f"  - 类型: {field.ttype}")
            print(f"  - 关联: {field.related}")
            print(f"  - 描述: {field.field_description}")
    
    # 检查stock.picking的所有字段类型
    print(f"\n=== stock.picking 所有字段类型 ===")
    all_fields = env['ir.model.fields'].search([
        ('model_id', '=', picking_model.id)
    ], order='name')
    
    for field in all_fields:
        if field.name in ['note', 'message', 'description', 'comment']:
            print(f"{field.name}: {field.ttype} ({field.field_description})")

print("\n检查完成")