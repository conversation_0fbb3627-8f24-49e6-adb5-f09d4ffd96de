<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 转库界面字段顺序调整 - 继承基础视图避免Studio干扰 -->
    <record id="view_stock_move_line_operation_tree_transfer_inherit" model="ir.ui.view">
        <field name="name">stock.move.line.operations.tree.transfer.inherit</field>
        <field name="model">stock.move.line</field>
        <field name="inherit_id" ref="stock.view_stock_move_line_operation_tree"/>
        <field name="arch" type="xml">
            <!-- 重新组织字段顺序，符合转库界面需求 -->
            <xpath expr="//field[@name='product_id']" position="replace">
                <field name="product_id" context="{'default_detailed_type': 'product'}" required="1" readonly="state == 'done' or move_id"/>
            </xpath>
            
            <!-- 添加Vendor Item No字段 -->
            <xpath expr="//field[@name='product_id']" position="after">
                <field name="x_studio_vendor_item_no" optional="show"/>
            </xpath>
            
            <!-- 调整To和Pick From位置 -->
            <xpath expr="//field[@name='location_dest_id']" position="replace"/>
            <xpath expr="//field[@name='location_id']" position="replace"/>
            
            <xpath expr="//field[@name='x_studio_vendor_item_no']" position="after">
                <field name="location_dest_id" string="To" column_invisible="parent.show_quant and parent.picking_code != 'internal'" readonly="package_level_id and parent.picking_type_entire_packs" domain="[('id', 'child_of', parent.location_dest_id), '|', ('company_id', '=', False), ('company_id', '=', company_id), ('usage', '!=', 'view')]" groups="stock.group_stock_multi_locations"/>
                <field name="location_id" string="Pick From" column_invisible="parent.show_quant" readonly="package_level_id and parent.picking_type_entire_packs" domain="[('id', 'child_of', parent.location_id), '|', ('company_id', '=', False), ('company_id', '=', company_id), ('usage', '!=', 'view')]" groups="stock.group_stock_multi_locations"/>
            </xpath>
            
            <!-- 添加Demand字段 -->
            <xpath expr="//field[@name='location_id']" position="after">
                <field name="move_id" string="Demand" readonly="1" optional="show"/>
            </xpath>
            
            <!-- 重新排序Quantity和Unit of Measure -->
            <xpath expr="//field[@name='quantity']" position="replace"/>
            <xpath expr="//field[@name='move_id']" position="after">
                <field name="quantity" string="Quantity" readonly="(state == 'done' and is_locked) or (package_level_id and parent.picking_type_entire_packs)" sum="Quantity"/>
                <field name="product_uom_id" options="{'no_open': True, 'no_create': True}" string="Unit of Measure" groups="uom.group_uom" readonly="(package_level_id and parent.picking_type_entire_packs) or (state == 'done' and id)"/>
            </xpath>
            
            <!-- 设置可选字段的默认显示状态 -->
            <xpath expr="//field[@name='lot_id']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            
            <xpath expr="//field[@name='lot_name']" position="attributes">
                <attribute name="optional">hide</attribute>
            </xpath>
            
            <!-- 添加自定义字段并设置默认隐藏 -->
            <xpath expr="//field[@name='product_uom_id']" position="after">
                <field name="x_studio_eta" optional="hide"/>
                <field name="x_studio_exmill_date_1" optional="hide"/>
                <field name="x_studio_exmill_date" optional="hide"/>
                <field name="secondary_uom_qty" optional="hide"/>
                <field name="secondary_uom_id" optional="hide"/>
                <field name="x_studio_po_description" optional="hide"/>
            </xpath>
            
        </field>
    </record>
</odoo>