<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- 收货界面字段增删改 - 继承详细操作视图 -->
    <record id="view_stock_move_line_detailed_operation_tree_receipt_inherit" model="ir.ui.view">
        <field name="name">stock.move.line.detailed.operation.tree.receipt.inherit</field>
        <field name="model">stock.move.line</field>
        <field name="inherit_id" ref="stock.view_stock_move_line_detailed_operation_tree"/>
        <field name="arch" type="xml">
            <!-- 重新组织整个字段顺序 -->
            <xpath expr="//tree" position="inside">
                <!-- 重新定义所有字段的顺序 -->
                <field name="product_id" context="{'default_detailed_type': 'product'}" required="1" readonly="state == 'done' or move_id"/>
                <field name="x_studio_vendor_item_no" optional="show"/>
                <field name="shipping_sch_id" optional="show"/>
                <field name="move_id" string="Demand" readonly="1" optional="show"/>
                <field name="gross_unit" optional="show"/>
                <field name="quantity" string="Quantity" readonly="(state == 'done' and is_locked) or (package_level_id and parent.picking_type_entire_packs)" sum="Quantity"/>
                <field name="selvedge_id" optional="show"/>
                <field name="lot_name" string="Lot/Serial Number Name" placeholder="e.g. SN000001" column_invisible="context.get('picking_code') != 'incoming' or context.get('show_lots_text')" readonly="package_level_id and parent.picking_type_entire_packs"/>
                <field name="location_dest_id" string="To" column_invisible="context.get('picking_code') == 'incoming'" readonly="package_level_id and parent.picking_type_entire_packs" domain="[('id', 'child_of', parent.location_dest_id), '|', ('company_id', '=', False), ('company_id', '=', company_id), ('usage', '!=', 'view')]" groups="stock.group_stock_multi_locations"/>
                <field name="product_uom_id" options="{'no_open': True, 'no_create': True}" string="Unit of Measure" groups="uom.group_uom" readonly="(package_level_id and parent.picking_type_entire_packs) or (state == 'done' and id)"/>
                
                <!-- 设置可选字段的显示状态 -->
                <field name="available_quantity" optional="hide"/>
                <field name="display_warning" optional="hide"/>
                <field name="x_studio_eta" optional="show"/>
                <field name="x_studio_exmill_date_1" optional="show"/>
                <field name="x_studio_exmill_date" optional="show"/>
                <field name="secondary_uom_qty" optional="hide"/>
                <field name="secondary_uom_id" optional="hide"/>
                <field name="x_studio_po_description" optional="hide"/>
                
                <!-- 隐藏原始字段，避免重复 -->
                <field name="lot_id" position="attributes">
                    <attribute name="column_invisible">True</attribute>
                </field>
            </xpath>
            
            <!-- 确保Shipping Schedule支持排序 -->
            <xpath expr="//field[@name='shipping_sch_id']" position="attributes">
                <attribute name="widget">many2one</attribute>
            </xpath>
            
        </field>
    </record>
    
    <!-- 清除Studio的干扰视图 -->
    <record id="studio_customization.view_move_line_tree_purchase_misc_jsi" model="ir.ui.view">
        <field name="active">false</field>
    </record>
    
</odoo>