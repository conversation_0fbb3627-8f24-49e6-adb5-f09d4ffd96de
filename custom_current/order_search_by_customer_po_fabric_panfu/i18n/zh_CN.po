# Chinese (Simplified) translation for order_search_by_customer_po_internal_ref_panfu
# Copyright (C) 2025 panfu
# This file is distributed under the same license as the order_search_by_customer_po_internal_ref_panfu package.
# <AUTHOR> <EMAIL>, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: order_search_by_customer_po_internal_ref_panfu 17.0.1.0.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-29 15:20:00+0000\n"
"PO-Revision-Date: 2025-07-29 15:20:00+0000\n"
"Last-Translator: panfu <<EMAIL>>\n"
"Language-Team: Chinese (Simplified)\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: order_search_by_customer_po_fabric_panfu
#: model_terms:ir.ui.view,arch_db:order_search_by_customer_po_fabric_panfu.portal_my_orders_search_form
msgid "Search by Order #, Customer PO, or Internal Reference..."
msgstr "搜索订单号、客户采购单号或产品内部编号..."

#. module: order_search_by_customer_po_fabric_panfu
#: model_terms:ir.ui.view,arch_db:order_search_by_customer_po_fabric_panfu.portal_my_orders_search_form
msgid "Search"
msgstr "搜索"

#. module: order_search_by_customer_po_fabric_panfu
#: model_terms:ir.ui.view,arch_db:order_search_by_customer_po_fabric_panfu.portal_my_orders_search_form
msgid "Clear"
msgstr "清除"
