# Chinese (Traditional) translation for order_search_by_customer_po_fabric_panfu
# Copyright (C) 2025 panfu
# This file is distributed under the same license as the order_search_by_customer_po_fabric_panfu package.
# <AUTHOR> <EMAIL>, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: order_search_by_customer_po_fabric_panfu 17.0.1.0.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-29 15:20:00+0000\n"
"PO-Revision-Date: 2025-07-29 15:20:00+0000\n"
"Last-Translator: panfu <<EMAIL>>\n"
"Language-Team: Chinese (Traditional)\n"
"Language: zh_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: order_search_by_customer_po_fabric_panfu
#: model_terms:ir.ui.view,arch_db:order_search_by_customer_po_fabric_panfu.portal_my_orders_search_form
msgid "Search by Order #, Customer PO, or Fabric #..."
msgstr "搜尋訂單號、客戶採購單號或面料編號..."

#. module: order_search_by_customer_po_fabric_panfu
#: model_terms:ir.ui.view,arch_db:order_search_by_customer_po_fabric_panfu.portal_my_orders_search_form
msgid "Search"
msgstr "搜尋"

#. module: order_search_by_customer_po_fabric_panfu
#: model_terms:ir.ui.view,arch_db:order_search_by_customer_po_fabric_panfu.portal_my_orders_search_form
msgid "Clear"
msgstr "清除"
