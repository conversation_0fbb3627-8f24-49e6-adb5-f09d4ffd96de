<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Extend the quotations template to add customer PO and internal reference columns (暂时隐藏) -->
    <template id="portal_my_quotations_extended" inherit_id="sale.portal_my_quotations" name="Extended Portal Quotations">
        <xpath expr="//thead/tr" position="inside">
            <th style="display: none;">Customer PO</th>
            <th style="display: none;">Internal Ref</th>
        </xpath>
        <xpath expr="//t[@t-foreach='quotations']/tr" position="inside">
            <td style="display: none;"><t t-esc="customer_po_agg_dict.get(quotation.id, '')"/></td>
            <td style="display: none;"><t t-esc="internal_reference_agg_dict.get(quotation.id, '')"/></td>
        </xpath>
    </template>

    <!-- Extend the orders template to add customer PO and internal reference columns (暂时隐藏) -->
    <template id="portal_my_orders_extended" inherit_id="sale.portal_my_orders" name="Extended Portal Orders">
        <xpath expr="//thead/tr" position="inside">
            <th style="display: none;">Customer PO</th>
            <th style="display: none;">Internal Ref</th>
        </xpath>
        <xpath expr="//t[@t-foreach='orders']/tr" position="inside">
            <td style="display: none;"><t t-esc="customer_po_agg_dict.get(order.id, '')"/></td>
            <td style="display: none;"><t t-esc="internal_reference_agg_dict.get(order.id, '')"/></td>
        </xpath>
    </template>

    <!-- 为订单页面添加专门的搜索表单 -->
    <template id="portal_my_orders_search_form" inherit_id="sale.portal_my_orders" name="Add Search Form to Orders">
        <!-- 在排序按钮后添加搜索表单 -->
        <xpath expr="//t[@t-call='portal.portal_searchbar']" position="after">
            <div class="row mb-3">
                <div class="col-12 d-flex justify-content-end">
                    <form method="get" class="d-flex">
                        <input type="search"
                               name="search"
                               class="form-control me-2"
                               id="order-search-input"
                               placeholder="搜索订单号、客户采购单号或产品内部编号..."
                               t-att-value="search or ''"
                               style="min-width: 400px; width: 400px;"/>
                        <button type="submit" class="btn btn-primary d-flex align-items-center" id="order-search-btn" style="white-space: nowrap; min-width: 80px;">
                            <i class="fa fa-search me-1"></i><span id="search-btn-text">搜索</span>
                        </button>
                        <t t-if="search">
                            <a href="/my/orders" class="btn btn-secondary ms-2 d-flex align-items-center" id="order-clear-btn" style="white-space: nowrap; min-width: 80px;">
                                <i class="fa fa-times me-1"></i><span id="clear-btn-text">清除</span>
                            </a>
                        </t>
                    </form>
                </div>
            </div>

            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    // 多语言文本配置
                    const translations = {
                        'zh_CN': {
                            placeholder: '搜索订单号、客户采购单号或产品内部编号...',
                            search: '搜索',
                            clear: '清除'
                        },
                        'zh_TW': {
                            placeholder: '搜尋訂單號、客戶採購單號或產品內部編號...',
                            search: '搜尋',
                            clear: '清除'
                        },
                        'en_US': {
                            placeholder: 'Search by Order #, Customer PO, or Internal Reference...',
                            search: 'Search',
                            clear: 'Clear'
                        }
                    };

                    // 检测当前语言
                    function getCurrentLanguage() {
                        const path = window.location.pathname;
                        if (path.startsWith('/zh_CN/')) return 'zh_CN';
                        if (path.startsWith('/zh_TW/')) return 'zh_TW';
                        return 'en_US'; // 默认英文
                    }

                    // 更新界面文本
                    function updateTexts() {
                        const lang = getCurrentLanguage();
                        const texts = translations[lang] || translations['en_US'];

                        const searchInput = document.getElementById('order-search-input');
                        const searchBtnText = document.getElementById('search-btn-text');
                        const clearBtnText = document.getElementById('clear-btn-text');

                        if (searchInput) {
                            searchInput.placeholder = texts.placeholder;
                        }
                        if (searchBtnText) {
                            searchBtnText.textContent = texts.search;
                        }
                        if (clearBtnText) {
                            clearBtnText.textContent = texts.clear;
                        }
                    }

                    // 初始化文本
                    updateTexts();

                    // 监听语言切换（如果有的话）
                    window.addEventListener('popstate', updateTexts);
                });
            </script>
        </xpath>
    </template>

    <!-- 保留原有扩展订单列表和表头的模板 -->
</odoo>