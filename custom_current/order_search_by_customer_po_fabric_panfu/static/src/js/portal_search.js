odoo.define('order_search_by_customer_po_internal_ref_panfu.portal_search', function (require) {
    'use strict';

    var publicWidget = require('web.public.widget');

    publicWidget.registry.PortalOrderSearch = publicWidget.Widget.extend({
        selector: '.o_portal_my_doc_table',
        
        start: function () {
            this._super.apply(this, arguments);
            this._addSearchForm();
        },

        _addSearchForm: function () {
            // 检查是否已经有搜索表单
            if (this.$el.find('.portal-search-form').length > 0) {
                return;
            }

            // 获取当前搜索参数
            var urlParams = new URLSearchParams(window.location.search);
            var searchValue = urlParams.get('search') || '';

            // 创建搜索表单HTML
            var searchFormHtml = `
                <div class="row mb-3 portal-search-form">
                    <div class="col-12">
                        <form method="get" class="d-flex">
                            <input type="search"
                                   name="search"
                                   class="form-control me-2"
                                   placeholder="搜索订单号、客户采购单号或产品内部编号..."
                                   value="${searchValue}"
                                   style="max-width: 400px;"/>
                            <button type="submit" class="btn btn-primary">
                                <i class="fa fa-search"></i> 搜索
                            </button>
                            ${searchValue ? `
                                <a href="/my/orders" class="btn btn-secondary ms-2">
                                    <i class="fa fa-times"></i> 清除
                                </a>
                            ` : ''}
                        </form>
                    </div>
                </div>
            `;

            // 在表格前插入搜索表单
            this.$el.before(searchFormHtml);
        },
    });

    return publicWidget.registry.PortalOrderSearch;
});
