#!/usr/bin/env python3
"""
测试 LB Rmks Flag 功能的脚本
使用 Odoo shell 运行此脚本来验证功能是否正常工作
"""

def test_lb_rmks_flag():
    """测试 LB Rmks Flag 功能"""
    print("开始测试 LB Rmks Flag 功能...")
    
    # 获取环境
    Partner = env['res.partner']
    SaleOrder = env['sale.order']
    Product = env['product.product']
    
    # 1. 创建测试客户
    test_partner = Partner.create({
        'name': 'Test Customer for LB Rmks',
        'is_company': True,
        'customer_rank': 1,
        'lb_rmks_flag': 'zheng'  # 设置为"正"
    })
    print(f"创建测试客户: {test_partner.name}, LB Flag: {test_partner.lb_rmks_flag}")
    
    # 2. 创建测试产品
    test_product = Product.search([('sale_ok', '=', True)], limit=1)
    if not test_product:
        test_product = Product.create({
            'name': 'Test Product for LB Rmks',
            'type': 'product',
            'sale_ok': True,
            'list_price': 100.0,
        })
    print(f"使用测试产品: {test_product.name}")
    
    # 3. 创建销售订单
    sale_order = SaleOrder.create({
        'partner_id': test_partner.id,
        'partner_shipping_id': test_partner.id,  # 设置送货地址
    })
    print(f"创建销售订单: {sale_order.name}")
    
    # 4. 添加订单行
    order_line = env['sale.order.line'].create({
        'order_id': sale_order.id,
        'product_id': test_product.id,
        'product_uom_qty': 1,
        'price_unit': test_product.list_price,
    })
    print(f"添加订单行: {order_line.product_id.name}")
    print(f"订单行备注: '{order_line.remarks}'")
    
    # 5. 验证结果
    expected_prefix = '正；'
    if order_line.remarks and order_line.remarks.startswith(expected_prefix):
        print(f"✅ 测试通过: 订单行备注正确添加了前缀 '{expected_prefix}'")
    else:
        print(f"❌ 测试失败: 订单行备注应该以 '{expected_prefix}' 开头，实际为: '{order_line.remarks}'")
        
        # 尝试手动应用前缀
        print("尝试手动应用前缀...")
        sale_order.action_apply_lb_rmks_prefix()
        order_line.refresh()
        print(f"手动应用后的备注: '{order_line.remarks}'")
    
    # 6. 测试"反"标记
    test_partner.lb_rmks_flag = 'fan'
    print(f"更改客户 LB Flag 为: {test_partner.lb_rmks_flag}")
    
    # 添加另一个订单行
    order_line2 = env['sale.order.line'].create({
        'order_id': sale_order.id,
        'product_id': test_product.id,
        'product_uom_qty': 2,
        'price_unit': test_product.list_price,
    })
    print(f"添加第二个订单行备注: '{order_line2.remarks}'")
    
    expected_prefix2 = '反；'
    if order_line2.remarks and order_line2.remarks.startswith(expected_prefix2):
        print(f"✅ 测试通过: 第二个订单行备注正确添加了前缀 '{expected_prefix2}'")
    else:
        print(f"❌ 测试失败: 第二个订单行备注应该以 '{expected_prefix2}' 开头，实际为: '{order_line2.remarks}'")
    
    # 7. 测试空标记
    test_partner.lb_rmks_flag = ''
    print(f"更改客户 LB Flag 为空")
    
    order_line3 = env['sale.order.line'].create({
        'order_id': sale_order.id,
        'product_id': test_product.id,
        'product_uom_qty': 3,
        'price_unit': test_product.list_price,
    })
    print(f"添加第三个订单行备注: '{order_line3.remarks}'")
    
    if not order_line3.remarks or not order_line3.remarks.startswith(('正；', '反；')):
        print("✅ 测试通过: 空标记时没有添加前缀")
    else:
        print(f"❌ 测试失败: 空标记时不应该添加前缀，实际为: '{order_line3.remarks}'")
    
    print("测试完成!")
    return sale_order

# 运行测试
if __name__ == '__main__':
    test_order = test_lb_rmks_flag()
