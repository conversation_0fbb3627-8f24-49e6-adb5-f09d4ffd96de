#!/bin/bash

# Odoo插件重命名脚本
# 使用方法: ./rename_plugin.sh 旧插件名 新插件名 任务编号

if [ $# -ne 3 ]; then
    echo "用法: $0 旧插件名 新插件名 任务编号"
    echo "示例: $0 stock_move_line_ui_enhancement stock_move_line_improved 1208337958369478"
    exit 1
fi

OLD_NAME="$1"
NEW_NAME="$2"
TASK_ID="$3"
AUTHOR="panfu"

OLD_DIR="${OLD_NAME}_${AUTHOR}_${TASK_ID}"
NEW_DIR="${NEW_NAME}_${AUTHOR}_${TASK_ID}"

echo "开始重命名插件..."
echo "旧名称: $OLD_NAME"
echo "新名称: $NEW_NAME"
echo "任务编号: $TASK_ID"

# 1. 检查目录是否存在
if [ ! -d "custom/$OLD_DIR" ]; then
    echo "错误: 目录 custom/$OLD_DIR 不存在"
    exit 1
fi

# 2. 重命名目录
echo "重命名目录..."
mv "custom/$OLD_DIR" "custom/$NEW_DIR"

# 3. 更新__manifest__.py
echo "更新__manifest__.py..."
sed -i '' "s/'name':.*/'name': '$NEW_NAME',/" "custom/$NEW_DIR/__manifest__.py"
sed -i '' "s/'description':.*/'description': '$NEW_NAME',/" "custom/$NEW_DIR/__manifest__.py"

# 4. 更新Git
echo "更新Git..."
git add "custom/$OLD_DIR" "custom/$NEW_DIR"
git commit -m "refactor: 重命名插件 $OLD_NAME 为 $NEW_NAME"

echo "插件重命名完成！"
echo "新目录: custom/$NEW_DIR"
echo "请手动重启Odoo服务并测试"